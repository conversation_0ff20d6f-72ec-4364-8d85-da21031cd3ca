/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Times New Roman', Times, serif;
    font-weight: bold;
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 50%, #87CEFA 100%);
    min-height: 100vh;
    color: #2c3e50;
    line-height: 1.6;
}

/* Header Styles */
.header {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(10px);
    border-bottom: 3px solid #FFD700;
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: 0 4px 20px rgba(70, 130, 180, 0.3);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 2rem;
    max-width: 1400px;
    margin: 0 auto;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.5rem;
    font-weight: 900;
    color: #4682B4;
    font-family: 'Times New Roman', Times, serif;
}

.logo i {
    font-size: 2rem;
}

.nav-menu {
    display: flex;
    gap: 0.5rem;
}

.nav-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    background: transparent;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    color: #64748b;
}

.nav-btn:hover {
    background: rgba(255, 215, 0, 0.2);
    color: #4682B4;
}

.nav-btn.active {
    background: linear-gradient(135deg, #4682B4, #87CEEB);
    color: white;
    box-shadow: 0 4px 12px rgba(70, 130, 180, 0.4);
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.btn-primary {
    background: linear-gradient(135deg, #FFD700, #FFA500);
    color: #2c3e50;
    border: 2px solid #4682B4;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 900;
    font-family: 'Times New Roman', Times, serif;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(255, 215, 0, 0.4);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 215, 0, 0.6);
    background: linear-gradient(135deg, #FFA500, #FFD700);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.9);
    color: #4682B4;
    border: 2px solid #87CEEB;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 700;
    font-family: 'Times New Roman', Times, serif;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background: #87CEEB;
    color: white;
}

.user-profile {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #4682B4, #87CEEB);
    border: 2px solid #FFD700;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.user-profile:hover {
    transform: scale(1.1);
}

/* Main Content */
.main-content {
    padding: 2rem;
    max-width: 1400px;
    margin: 0 auto;
}

.view {
    display: none;
    animation: fadeIn 0.3s ease;
}

.view.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Dashboard Styles */
.dashboard-header h1 {
    font-size: 2.5rem;
    margin-bottom: 2rem;
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.stat-card {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(10px);
    padding: 2rem;
    border-radius: 16px;
    border: 2px solid #FFD700;
    display: flex;
    align-items: center;
    gap: 1.5rem;
    box-shadow: 0 8px 32px rgba(70, 130, 180, 0.2);
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(255, 215, 0, 0.3);
    border-color: #4682B4;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.stat-card:nth-child(1) .stat-icon { background: linear-gradient(135deg, #4682B4, #87CEEB); }
.stat-card:nth-child(2) .stat-icon { background: linear-gradient(135deg, #FFD700, #FFA500); }
.stat-card:nth-child(3) .stat-icon { background: linear-gradient(135deg, #87CEEB, #4682B4); }
.stat-card:nth-child(4) .stat-icon { background: linear-gradient(135deg, #FFA500, #FFD700); }

.stat-number {
    font-size: 2rem;
    font-weight: 900;
    font-family: 'Times New Roman', Times, serif;
    color: #2c3e50;
    display: block;
}

.stat-label {
    color: #4682B4;
    font-weight: 700;
    font-family: 'Times New Roman', Times, serif;
}

.dashboard-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

.dashboard-left, .dashboard-right {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.recent-projects, .progress-overview, .upcoming-deadlines, .activity-feed {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(10px);
    padding: 2rem;
    border-radius: 16px;
    border: 2px solid #87CEEB;
    box-shadow: 0 8px 32px rgba(70, 130, 180, 0.15);
}

.recent-projects h2, .progress-overview h2, .upcoming-deadlines h2 {
    margin-bottom: 1.5rem;
    color: #4682B4;
    font-size: 1.5rem;
    font-weight: 900;
    font-family: 'Times New Roman', Times, serif;
}

/* Projects View */
.projects-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.projects-header h1 {
    font-size: 2.5rem;
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.view-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.view-switcher {
    display: flex;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 0.25rem;
}

.view-btn {
    padding: 0.5rem 1rem;
    border: none;
    background: transparent;
    color: white;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.view-btn.active {
    background: rgba(255, 255, 255, 0.9);
    color: #2c3e50;
}

.filter-controls {
    display: flex;
    gap: 1rem;
}

.filter-controls select, .filter-controls input {
    padding: 0.75rem;
    border: none;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.9);
    color: #2c3e50;
}

.projects-container {
    display: grid;
    gap: 1.5rem;
}

.projects-container.grid-view {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
}

.projects-container.list-view {
    grid-template-columns: 1fr;
}

/* Project Card */
.project-card {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 8px 32px rgba(70, 130, 180, 0.15);
    transition: all 0.3s ease;
    cursor: pointer;
    border: 3px solid #FFD700;
    border-left: 6px solid #4682B4;
}

.project-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(255, 215, 0, 0.3);
    border-color: #87CEEB;
    border-left-color: #4682B4;
}

.project-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.project-title {
    font-size: 1.25rem;
    font-weight: 900;
    font-family: 'Times New Roman', Times, serif;
    color: #4682B4;
    margin-bottom: 0.5rem;
}

.project-description {
    color: #7f8c8d;
    font-size: 0.9rem;
    margin-bottom: 1.5rem;
    line-height: 1.5;
}

.project-progress {
    margin-bottom: 1rem;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #ecf0f1;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #FFD700, #FFA500);
    border-radius: 4px;
    transition: width 0.3s ease;
}

.project-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.85rem;
    color: #7f8c8d;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-badge.active { background: #E6F3FF; color: #4682B4; font-weight: bold; }
.status-badge.completed { background: #FFF8DC; color: #B8860B; font-weight: bold; }
.status-badge.on-hold { background: #F0F8FF; color: #87CEEB; font-weight: bold; }

/* Project Detail View */
.project-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 2rem;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.project-title-section {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.back-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #3498db;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.back-btn:hover {
    background: rgba(52, 152, 219, 0.1);
}

#projectTitle {
    font-size: 2rem;
    color: #2c3e50;
    margin: 0;
}

.project-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.project-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* Kanban Board */
.kanban-board {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
    padding: 2rem 0;
}

.kanban-column {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    border: 2px solid #87CEEB;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(70, 130, 180, 0.15);
    min-height: 600px;
}

.column-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #ecf0f1;
}

.column-header h3 {
    color: #4682B4;
    font-size: 1.1rem;
    font-weight: 900;
    font-family: 'Times New Roman', Times, serif;
}

.task-count {
    background: linear-gradient(135deg, #FFD700, #FFA500);
    color: #2c3e50;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 900;
    font-family: 'Times New Roman', Times, serif;
}

.column-content {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

/* Task Card */
.task-card {
    background: white;
    border-radius: 8px;
    border: 2px solid #FFD700;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(70, 130, 180, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;
    border-left: 4px solid #4682B4;
}

.task-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(255, 215, 0, 0.3);
    border-color: #87CEEB;
    border-left-color: #4682B4;
}

.task-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.5rem;
    gap: 0.5rem;
}

.task-title {
    font-weight: 900;
    font-family: 'Times New Roman', Times, serif;
    color: #4682B4;
    flex: 1;
    line-height: 1.3;
}

.task-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
    margin-bottom: 0.75rem;
}

.task-tag {
    background: linear-gradient(135deg, #87CEEB, #4682B4);
    color: white;
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 700;
    font-family: 'Times New Roman', Times, serif;
}

.task-estimate {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.75rem;
    color: #87CEEB;
    font-weight: 700;
}

.task-assignee {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 0.75rem;
    padding-top: 0.75rem;
    border-top: 1px solid #f0f0f0;
}

.assignee-avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.7rem;
    font-weight: 900;
    font-family: 'Times New Roman', Times, serif;
    border: 2px solid #FFD700;
}

.assignee-name {
    font-size: 0.8rem;
    color: #4682B4;
    font-weight: 700;
    font-family: 'Times New Roman', Times, serif;
}

.task-description {
    color: #7f8c8d;
    font-size: 0.9rem;
    margin-bottom: 1rem;
    line-height: 1.4;
}

.task-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.8rem;
    color: #95a5a6;
}

.priority-badge {
    padding: 0.2rem 0.6rem;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
}

.priority-badge.low { background: #d5f4e6; color: #27ae60; }
.priority-badge.medium { background: #fef3cd; color: #f39c12; }
.priority-badge.high { background: #fde2e1; color: #e74c3c; }
.priority-badge.critical { background: #f3e2f3; color: #8e44ad; }

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    z-index: 2000;
    animation: fadeIn 0.3s ease;
}

.modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    border-radius: 16px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2rem;
    border-bottom: 1px solid #ecf0f1;
}

.modal-header h2 {
    color: #2c3e50;
    margin: 0;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #95a5a6;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.close-btn:hover {
    background: #ecf0f1;
    color: #2c3e50;
}

.modal-body {
    padding: 2rem;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    padding: 2rem;
    border-top: 1px solid #ecf0f1;
}

/* Form Styles */
.form-group {
    margin-bottom: 1.5rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #2c3e50;
}

.form-group input, .form-group select, .form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #ecf0f1;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-group input:focus, .form-group select:focus, .form-group textarea:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.color-picker {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.color-picker input[type="color"] {
    width: 60px;
    height: 40px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        padding: 1rem;
        flex-direction: column;
        gap: 1rem;
    }
    
    .nav-menu {
        order: 3;
        width: 100%;
        justify-content: center;
    }
    
    .main-content {
        padding: 1rem;
    }
    
    .dashboard-content {
        grid-template-columns: 1fr;
    }
    
    .kanban-board {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .projects-container.grid-view {
        grid-template-columns: 1fr;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
}

/* Chart Container */
.chart-container {
    width: 100%;
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    border-radius: 8px;
    border: 2px dashed #dee2e6;
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Drag and Drop */
.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
}

.drop-zone {
    background: rgba(52, 152, 219, 0.1);
    border: 2px dashed #3498db;
}

/* Task List View Styles */
.task-list-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.task-list-header {
    background: #f8f9fa;
    border-bottom: 2px solid #ecf0f1;
}

.task-list-row {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr 1fr;
    gap: 1rem;
    padding: 1rem;
    border-bottom: 1px solid #ecf0f1;
    align-items: center;
}

.task-list-header .task-list-row {
    font-weight: 600;
    color: #2c3e50;
    background: #f8f9fa;
}

.task-list-body .task-list-row:hover {
    background: #f8f9fa;
}

.task-col-name .task-name {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.25rem;
}

.task-col-name .task-description {
    font-size: 0.85rem;
    color: #7f8c8d;
}

/* Gantt Chart Styles */
.gantt-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    overflow-x: auto;
}

.gantt-chart {
    min-width: 800px;
}

/* Calendar Styles */
.calendar-container, .task-calendar {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    border: 2px solid #87CEEB;
    padding: 2rem;
    box-shadow: 0 8px 32px rgba(70, 130, 180, 0.15);
    min-height: 400px;
}

.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1px;
    background: #87CEEB;
    border-radius: 8px;
    overflow: hidden;
}

.calendar-header-row {
    display: contents;
}

.calendar-day-header {
    background: linear-gradient(135deg, #4682B4, #87CEEB);
    color: white;
    padding: 1rem;
    text-align: center;
    font-weight: 900;
    font-family: 'Times New Roman', Times, serif;
    font-size: 0.9rem;
}

.calendar-day {
    background: white;
    min-height: 120px;
    padding: 0.5rem;
    display: flex;
    flex-direction: column;
    cursor: pointer;
    transition: all 0.3s ease;
}

.calendar-day:hover {
    background: #F0F8FF;
}

.calendar-day.today {
    background: #FFF8DC;
    border: 2px solid #FFD700;
}

.calendar-day.empty {
    background: #f8f9fa;
    cursor: default;
}

.calendar-day-number {
    font-weight: 900;
    font-family: 'Times New Roman', Times, serif;
    color: #4682B4;
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.calendar-day-tasks {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.calendar-task {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    color: white;
    font-weight: bold;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.calendar-task-name {
    display: block;
    font-weight: 900;
}

.calendar-task-project {
    display: block;
    font-size: 0.7rem;
    opacity: 0.9;
}

/* Team View Styles */
.team-container {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    border: 2px solid #87CEEB;
    padding: 2rem;
    box-shadow: 0 8px 32px rgba(70, 130, 180, 0.15);
}

.team-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #FFD700;
}

.team-header h3 {
    color: #4682B4;
    font-size: 1.5rem;
    font-weight: 900;
    font-family: 'Times New Roman', Times, serif;
    margin: 0;
}

.team-stats {
    color: #87CEEB;
    font-weight: 700;
    font-family: 'Times New Roman', Times, serif;
}

.team-members-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1.5rem;
}

.team-member-card {
    background: white;
    border-radius: 12px;
    border: 2px solid #FFD700;
    padding: 1.5rem;
    box-shadow: 0 4px 16px rgba(70, 130, 180, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;
}

.team-member-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(255, 215, 0, 0.3);
    border-color: #4682B4;
}

.team-member-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.team-member-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 900;
    font-family: 'Times New Roman', Times, serif;
    font-size: 1.2rem;
    border: 3px solid #FFD700;
}

.team-member-info h4 {
    margin: 0;
    color: #4682B4;
    font-weight: 900;
    font-family: 'Times New Roman', Times, serif;
}

.team-member-role {
    color: #87CEEB;
    font-size: 0.9rem;
    font-weight: 700;
    text-transform: uppercase;
}

.team-member-email {
    color: #666;
    font-size: 0.85rem;
    margin-bottom: 1rem;
}

.team-member-skills {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.skill-tag {
    background: linear-gradient(135deg, #87CEEB, #4682B4);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 700;
    font-family: 'Times New Roman', Times, serif;
}

.team-member-stats {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    font-size: 0.85rem;
}

.stat-item {
    text-align: center;
}

.stat-value {
    display: block;
    font-weight: 900;
    font-family: 'Times New Roman', Times, serif;
    color: #4682B4;
    font-size: 1.1rem;
}

.stat-label {
    color: #87CEEB;
    font-weight: 700;
}

/* Task Detail Modal Styles */
.task-detail-modal {
    max-width: 900px;
    width: 95%;
}

.task-detail-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.task-detail-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
}

.task-detail-main {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.task-detail-section h4 {
    color: #4682B4;
    font-weight: 900;
    font-family: 'Times New Roman', Times, serif;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.task-detail-sidebar {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 1.5rem;
    border: 2px solid #87CEEB;
}

.task-detail-info {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 2rem;
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.info-item label {
    font-weight: 700;
    font-family: 'Times New Roman', Times, serif;
    color: #4682B4;
    font-size: 0.9rem;
}

.assignee-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.time-tracking {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-small {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
}

.task-comments {
    max-height: 300px;
    overflow-y: auto;
    margin-bottom: 1rem;
}

.comment-item {
    background: white;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
    padding: 1rem;
    margin-bottom: 1rem;
}

.comment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.comment-author {
    font-weight: 700;
    color: #4682B4;
    font-family: 'Times New Roman', Times, serif;
}

.comment-date {
    font-size: 0.8rem;
    color: #87CEEB;
}

.comment-text {
    color: #2c3e50;
    line-height: 1.5;
}

.add-comment {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.add-comment textarea {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #87CEEB;
    border-radius: 8px;
    font-family: 'Times New Roman', Times, serif;
    resize: vertical;
}

.add-comment textarea:focus {
    outline: none;
    border-color: #4682B4;
    box-shadow: 0 0 0 3px rgba(70, 130, 180, 0.1);
}

.tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.task-detail-tags label {
    font-weight: 700;
    font-family: 'Times New Roman', Times, serif;
    color: #4682B4;
    margin-bottom: 0.5rem;
    display: block;
}

/* Notifications Styles */
.notification-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: linear-gradient(135deg, #FFD700, #FFA500);
    color: #2c3e50;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
    font-weight: 900;
    font-family: 'Times New Roman', Times, serif;
    border: 2px solid white;
}

#notificationsBtn {
    position: relative;
}

.notifications-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    width: 350px;
    background: white;
    border-radius: 12px;
    border: 2px solid #87CEEB;
    box-shadow: 0 8px 32px rgba(70, 130, 180, 0.2);
    z-index: 2000;
    display: none;
    max-height: 400px;
    overflow: hidden;
}

.notifications-dropdown.active {
    display: block;
}

.notifications-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-bottom: 2px solid #FFD700;
    background: linear-gradient(135deg, #4682B4, #87CEEB);
    color: white;
}

.notifications-header h3 {
    margin: 0;
    font-weight: 900;
    font-family: 'Times New Roman', Times, serif;
}

.btn-link {
    background: none;
    border: none;
    color: white;
    text-decoration: underline;
    cursor: pointer;
    font-weight: 700;
    font-family: 'Times New Roman', Times, serif;
}

.notifications-list {
    max-height: 300px;
    overflow-y: auto;
}

.notification-item {
    padding: 1rem;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: all 0.3s ease;
}

.notification-item:hover {
    background: #f8f9fa;
}

.notification-item.unread {
    background: #FFF8DC;
    border-left: 4px solid #FFD700;
}

.notification-content {
    display: flex;
    gap: 1rem;
}

.notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 900;
    flex-shrink: 0;
}

.notification-text {
    flex: 1;
}

.notification-title {
    font-weight: 700;
    color: #4682B4;
    font-family: 'Times New Roman', Times, serif;
    margin-bottom: 0.25rem;
}

.notification-message {
    color: #666;
    font-size: 0.9rem;
    line-height: 1.4;
}

.notification-time {
    color: #87CEEB;
    font-size: 0.8rem;
    margin-top: 0.25rem;
}

.notifications-footer {
    padding: 1rem;
    text-align: center;
    border-top: 1px solid #f0f0f0;
    background: #f8f9fa;
}

/* Activity Feed Styles */
.activity-list {
    max-height: 300px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    gap: 1rem;
    padding: 1rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.8rem;
    flex-shrink: 0;
}

.activity-content {
    flex: 1;
}

.activity-text {
    color: #2c3e50;
    font-size: 0.9rem;
    line-height: 1.4;
    margin-bottom: 0.25rem;
}

.activity-time {
    color: #87CEEB;
    font-size: 0.8rem;
    font-weight: 700;
}

.activity-user {
    color: #4682B4;
    font-weight: 700;
    font-family: 'Times New Roman', Times, serif;
}

/* Help Modal Styles */
.help-section {
    margin-bottom: 2rem;
}

.help-section h3 {
    color: #4682B4;
    font-weight: 900;
    font-family: 'Times New Roman', Times, serif;
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.shortcuts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.shortcut-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #87CEEB;
}

kbd {
    background: linear-gradient(135deg, #4682B4, #87CEEB);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-family: 'Times New Roman', Times, serif;
    font-weight: 700;
    font-size: 0.8rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.features-list {
    list-style: none;
    padding: 0;
}

.features-list li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #f0f0f0;
    color: #2c3e50;
}

.features-list li:last-child {
    border-bottom: none;
}

.features-list strong {
    color: #4682B4;
    font-weight: 900;
    font-family: 'Times New Roman', Times, serif;
}

/* Responsive improvements */
@media (max-width: 768px) {
    .task-detail-content {
        grid-template-columns: 1fr;
    }

    .shortcuts-grid {
        grid-template-columns: 1fr;
    }

    .notifications-dropdown {
        width: 300px;
        right: -50px;
    }

    .team-members-grid {
        grid-template-columns: 1fr;
    }
}

.calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.calendar-header h1 {
    font-size: 2.5rem;
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.calendar-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
    background: rgba(255, 255, 255, 0.2);
    padding: 0.5rem 1rem;
    border-radius: 12px;
}

#currentMonth {
    color: white;
    font-weight: 600;
    font-size: 1.1rem;
    min-width: 150px;
    text-align: center;
}

/* Analytics Styles */
.analytics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
}

.analytics-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.analytics-card h3 {
    margin-bottom: 1.5rem;
    color: #2c3e50;
    font-size: 1.25rem;
}

/* Deadline Styles */
.deadline-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 0.5rem;
    background: #f8f9fa;
    transition: all 0.3s ease;
}

.deadline-item:hover {
    background: #ecf0f1;
}

.deadline-item.overdue {
    background: #fde2e1;
    border-left: 4px solid #e74c3c;
}

.deadline-info .task-name {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.25rem;
}

.deadline-info .project-name {
    font-size: 0.85rem;
    font-weight: 500;
}

.deadline-date .overdue-text {
    color: #e74c3c;
    font-weight: 600;
}

.deadline-date .due-text {
    color: #7f8c8d;
}

/* Empty State Styles */
.empty-state {
    text-align: center;
    padding: 3rem;
    color: #7f8c8d;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    color: #bdc3c7;
}

.empty-state h3 {
    margin-bottom: 0.5rem;
    color: #2c3e50;
}

.empty-state p {
    margin-bottom: 2rem;
}

/* Progress Text */
.progress-text {
    font-size: 0.85rem;
    color: #7f8c8d;
    margin-top: 0.5rem;
    display: block;
}

/* Task Assignee */
.task-assignee {
    font-size: 0.8rem;
    color: #7f8c8d;
    margin-top: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

/* Due Date Styles */
.due-date {
    font-size: 0.75rem;
}

.due-date.overdue {
    color: #e74c3c;
    font-weight: 600;
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    padding: 1rem 1.5rem;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    transform: translateX(400px);
    transition: all 0.3s ease;
    z-index: 3000;
    border-left: 4px solid #3498db;
}

.notification.success {
    border-left-color: #27ae60;
    color: #27ae60;
}

.notification.error {
    border-left-color: #e74c3c;
    color: #e74c3c;
}

.notification.show {
    transform: translateX(0);
}

/* Animations */
.slide-in {
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

.bounce-in {
    animation: bounceIn 0.5s ease;
}

@keyframes bounceIn {
    0% { transform: scale(0.3); opacity: 0; }
    50% { transform: scale(1.05); }
    70% { transform: scale(0.9); }
    100% { transform: scale(1); opacity: 1; }
}

/* Additional Responsive Design */
@media (max-width: 1024px) {
    .analytics-grid {
        grid-template-columns: 1fr;
    }

    .task-list-row {
        grid-template-columns: 2fr 1fr 1fr;
        gap: 0.5rem;
    }

    .task-col-assignee, .task-col-due {
        display: none;
    }
}

@media (max-width: 480px) {
    .dashboard-stats {
        grid-template-columns: 1fr;
    }

    .stat-card {
        padding: 1.5rem;
    }

    .project-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }

    .project-controls {
        width: 100%;
        justify-content: space-between;
    }

    .view-switcher {
        flex-wrap: wrap;
    }

    .task-list-row {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .task-list-row > div {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .task-list-row > div::before {
        content: attr(data-label);
        font-weight: 600;
        color: #7f8c8d;
    }
}
