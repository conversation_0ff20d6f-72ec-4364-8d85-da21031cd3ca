<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Drag & Drop Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .kanban-board {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            margin: 20px 0;
        }
        .kanban-column {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            min-height: 300px;
        }
        .column-header {
            text-align: center;
            margin-bottom: 15px;
            font-weight: bold;
            color: #333;
        }
        .column-content {
            min-height: 250px;
            border: 2px dashed transparent;
            border-radius: 8px;
            padding: 10px;
            transition: all 0.2s ease;
        }
        .task-card {
            background: white;
            border: 2px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            cursor: grab;
            transition: all 0.2s ease;
            user-select: none;
        }
        .task-card:hover {
            border-color: #4682B4;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .task-card:active {
            cursor: grabbing;
        }
        .dragging {
            opacity: 0.6;
            transform: rotate(3deg) scale(1.02);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
            z-index: 1000;
        }
        .drop-zone {
            background: rgba(70, 130, 180, 0.1);
            border: 2px dashed #4682B4 !important;
        }
        .task-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .task-description {
            color: #666;
            font-size: 0.9em;
        }
        .instructions {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .status {
            padding: 5px 10px;
            border-radius: 4px;
            font-weight: bold;
            margin: 10px 0;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎯 Drag & Drop Test</h1>
        
        <div class="instructions">
            <h3>Instructions:</h3>
            <p>1. Try dragging the task cards below between different columns</p>
            <p>2. You should see visual feedback when dragging and hovering over drop zones</p>
            <p>3. Check the console (F12) for debug messages</p>
            <p>4. The status indicator below will show if drag & drop is working</p>
        </div>

        <div id="status" class="status info">Status: Ready to test</div>

        <div class="kanban-board">
            <div class="kanban-column" data-status="todo">
                <div class="column-header">To Do</div>
                <div class="column-content" id="todoColumn">
                    <div class="task-card" draggable="true" data-task-id="test-1">
                        <div class="task-title">Test Task 1</div>
                        <div class="task-description">This is a test task for drag & drop</div>
                    </div>
                    <div class="task-card" draggable="true" data-task-id="test-2">
                        <div class="task-title">Test Task 2</div>
                        <div class="task-description">Another test task</div>
                    </div>
                </div>
            </div>

            <div class="kanban-column" data-status="in-progress">
                <div class="column-header">In Progress</div>
                <div class="column-content" id="inProgressColumn">
                    <div class="task-card" draggable="true" data-task-id="test-3">
                        <div class="task-title">Test Task 3</div>
                        <div class="task-description">Task in progress</div>
                    </div>
                </div>
            </div>

            <div class="kanban-column" data-status="review">
                <div class="column-header">Review</div>
                <div class="column-content" id="reviewColumn">
                </div>
            </div>

            <div class="kanban-column" data-status="done">
                <div class="column-header">Done</div>
                <div class="column-content" id="doneColumn">
                </div>
            </div>
        </div>

        <button onclick="resetTest()" style="padding: 10px 20px; background: #4682B4; color: white; border: none; border-radius: 5px; cursor: pointer;">Reset Test</button>
    </div>

    <script>
        let draggedTaskId = null;
        let dragCount = 0;
        let dropCount = 0;

        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
        }

        function setupDragAndDrop() {
            const taskCards = document.querySelectorAll('.task-card');
            const columns = document.querySelectorAll('.column-content');

            taskCards.forEach(card => {
                card.addEventListener('dragstart', onDragStart);
                card.addEventListener('dragend', onDragEnd);
            });

            columns.forEach(column => {
                column.addEventListener('dragover', onDragOver);
                column.addEventListener('dragleave', onDragLeave);
                column.addEventListener('drop', onDrop);
            });

            updateStatus('Drag & Drop initialized successfully', 'success');
        }

        function onDragStart(e) {
            draggedTaskId = e.target.dataset.taskId;
            e.target.classList.add('dragging');
            e.dataTransfer.effectAllowed = 'move';
            e.dataTransfer.setData('text/plain', draggedTaskId);
            
            dragCount++;
            console.log('Drag started for task:', draggedTaskId);
            updateStatus(`Dragging task ${draggedTaskId} (Drag #${dragCount})`, 'info');
        }

        function onDragEnd(e) {
            e.target.classList.remove('dragging');
            document.querySelectorAll('.column-content').forEach(col => {
                col.classList.remove('drop-zone');
            });
            
            console.log('Drag ended');
            updateStatus(`Drag ended for task ${draggedTaskId}`, 'info');
            draggedTaskId = null;
        }

        function onDragOver(e) {
            e.preventDefault();
            e.dataTransfer.dropEffect = 'move';
            e.currentTarget.classList.add('drop-zone');
        }

        function onDragLeave(e) {
            const rect = e.currentTarget.getBoundingClientRect();
            const x = e.clientX;
            const y = e.clientY;
            
            if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
                e.currentTarget.classList.remove('drop-zone');
            }
        }

        function onDrop(e) {
            e.preventDefault();
            e.currentTarget.classList.remove('drop-zone');
            
            const taskId = e.dataTransfer.getData('text/plain') || draggedTaskId;
            const newStatus = e.currentTarget.closest('.kanban-column').dataset.status;
            
            dropCount++;
            console.log('Drop event - Task ID:', taskId, 'New Status:', newStatus);
            updateStatus(`✅ Drop successful! Task ${taskId} moved to ${newStatus} (Drop #${dropCount})`, 'success');
            
            // Move the task element
            const taskElement = document.querySelector(`[data-task-id="${taskId}"]`);
            if (taskElement) {
                e.currentTarget.appendChild(taskElement);
            }
        }

        function resetTest() {
            // Reset all tasks to original positions
            const todoColumn = document.getElementById('todoColumn');
            const inProgressColumn = document.getElementById('inProgressColumn');
            
            todoColumn.innerHTML = `
                <div class="task-card" draggable="true" data-task-id="test-1">
                    <div class="task-title">Test Task 1</div>
                    <div class="task-description">This is a test task for drag & drop</div>
                </div>
                <div class="task-card" draggable="true" data-task-id="test-2">
                    <div class="task-title">Test Task 2</div>
                    <div class="task-description">Another test task</div>
                </div>
            `;
            
            inProgressColumn.innerHTML = `
                <div class="task-card" draggable="true" data-task-id="test-3">
                    <div class="task-title">Test Task 3</div>
                    <div class="task-description">Task in progress</div>
                </div>
            `;
            
            document.getElementById('reviewColumn').innerHTML = '';
            document.getElementById('doneColumn').innerHTML = '';
            
            dragCount = 0;
            dropCount = 0;
            setupDragAndDrop();
            updateStatus('Test reset - ready to test again', 'info');
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', setupDragAndDrop);
    </script>
</body>
</html>
