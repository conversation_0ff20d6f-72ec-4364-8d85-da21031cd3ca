<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ProjectFlow - Visual Project Management</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <!-- Header Navigation -->
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <i class="fas fa-project-diagram"></i>
                <span>ProjectFlow</span>
            </div>
            <nav class="nav-menu">
                <button class="nav-btn active" data-view="dashboard">
                    <i class="fas fa-tachometer-alt"></i>
                    Dashboard
                </button>
                <button class="nav-btn" data-view="projects">
                    <i class="fas fa-folder-open"></i>
                    Projects
                </button>
                <button class="nav-btn" data-view="calendar">
                    <i class="fas fa-calendar-alt"></i>
                    Calendar
                </button>
                <button class="nav-btn" data-view="analytics">
                    <i class="fas fa-chart-line"></i>
                    Analytics
                </button>
            </nav>
            <div class="header-actions">
                <button class="btn-secondary" id="notificationsBtn">
                    <i class="fas fa-bell"></i>
                    <span id="notificationCount" class="notification-badge">3</span>
                </button>
                <button class="btn-secondary" id="helpBtn" title="Help & Shortcuts">
                    <i class="fas fa-question-circle"></i>
                </button>
                <button class="btn-primary" id="newProjectBtn">
                    <i class="fas fa-plus"></i>
                    New Project
                </button>
                <div class="user-profile">
                    <i class="fas fa-user-circle"></i>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content Area -->
    <main class="main-content">
        <!-- Dashboard View -->
        <section id="dashboard-view" class="view active">
            <div class="dashboard-header">
                <h1>Dashboard Overview</h1>
                <div class="dashboard-stats">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-project-diagram"></i>
                        </div>
                        <div class="stat-info">
                            <span class="stat-number" id="totalProjects">0</span>
                            <span class="stat-label">Total Projects</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-tasks"></i>
                        </div>
                        <div class="stat-info">
                            <span class="stat-number" id="totalTasks">0</span>
                            <span class="stat-label">Active Tasks</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-info">
                            <span class="stat-number" id="overdueTasks">0</span>
                            <span class="stat-label">Overdue</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-info">
                            <span class="stat-number" id="completedTasks">0</span>
                            <span class="stat-label">Completed</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="dashboard-content">
                <div class="dashboard-left">
                    <div class="recent-projects">
                        <h2>Recent Projects</h2>
                        <div id="recentProjectsList" class="projects-list">
                            <!-- Recent projects will be populated here -->
                        </div>
                    </div>
                </div>
                
                <div class="dashboard-right">
                    <div class="progress-overview">
                        <h2>Progress Overview</h2>
                        <div id="progressChart" class="chart-container">
                            <!-- SVG Progress Chart will be inserted here -->
                        </div>
                    </div>

                    <div class="upcoming-deadlines">
                        <h2>Upcoming Deadlines</h2>
                        <div id="deadlinesList" class="deadlines-list">
                            <!-- Upcoming deadlines will be populated here -->
                        </div>
                    </div>

                    <div class="activity-feed">
                        <h2>Recent Activity</h2>
                        <div id="activityFeed" class="activity-list">
                            <!-- Activity feed will be populated here -->
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Projects View -->
        <section id="projects-view" class="view">
            <div class="projects-header">
                <h1>Projects</h1>
                <div class="view-controls">
                    <div class="view-switcher">
                        <button class="view-btn active" data-project-view="grid">
                            <i class="fas fa-th"></i>
                        </button>
                        <button class="view-btn" data-project-view="list">
                            <i class="fas fa-list"></i>
                        </button>
                    </div>
                    <div class="filter-controls">
                        <select id="statusFilter">
                            <option value="all">All Status</option>
                            <option value="active">Active</option>
                            <option value="completed">Completed</option>
                            <option value="on-hold">On Hold</option>
                        </select>
                        <input type="text" id="searchProjects" placeholder="Search projects...">
                    </div>
                </div>
            </div>
            
            <div id="projectsContainer" class="projects-container grid-view">
                <!-- Projects will be populated here -->
            </div>
        </section>

        <!-- Single Project View -->
        <section id="project-detail-view" class="view">
            <div class="project-header">
                <div class="project-title-section">
                    <button class="back-btn" id="backToProjects">
                        <i class="fas fa-arrow-left"></i>
                    </button>
                    <h1 id="projectTitle">Project Name</h1>
                    <div class="project-status">
                        <span id="projectStatus" class="status-badge">Active</span>
                    </div>
                </div>
                
                <div class="project-controls">
                    <div class="view-switcher">
                        <button class="view-btn active" data-task-view="kanban">
                            <i class="fas fa-columns"></i>
                            Kanban
                        </button>
                        <button class="view-btn" data-task-view="gantt">
                            <i class="fas fa-chart-gantt"></i>
                            Gantt
                        </button>
                        <button class="view-btn" data-task-view="calendar">
                            <i class="fas fa-calendar"></i>
                            Calendar
                        </button>
                        <button class="view-btn" data-task-view="list">
                            <i class="fas fa-list"></i>
                            List
                        </button>
                        <button class="view-btn" data-task-view="team">
                            <i class="fas fa-users"></i>
                            Team
                        </button>
                    </div>
                    <div class="project-actions">
                        <button class="btn-secondary" id="addTeamMemberBtn">
                            <i class="fas fa-user-plus"></i>
                            Add Member
                        </button>
                        <button class="btn-secondary" id="testDragDropBtn" onclick="projectManager.testDragAndDrop()" title="Test Drag & Drop">
                            <i class="fas fa-bug"></i>
                            Test D&D
                        </button>
                        <button class="btn-primary" id="newTaskBtn">
                            <i class="fas fa-plus"></i>
                            Add Task
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="project-content">
                <!-- Kanban View -->
                <div id="kanban-view" class="task-view active">
                    <div class="kanban-board">
                        <div class="kanban-column" data-status="todo">
                            <div class="column-header">
                                <h3>To Do</h3>
                                <span class="task-count">0</span>
                            </div>
                            <div class="column-content" id="todoColumn">
                                <!-- Tasks will be populated here -->
                            </div>
                        </div>
                        
                        <div class="kanban-column" data-status="in-progress">
                            <div class="column-header">
                                <h3>In Progress</h3>
                                <span class="task-count">0</span>
                            </div>
                            <div class="column-content" id="inProgressColumn">
                                <!-- Tasks will be populated here -->
                            </div>
                        </div>
                        
                        <div class="kanban-column" data-status="review">
                            <div class="column-header">
                                <h3>Review</h3>
                                <span class="task-count">0</span>
                            </div>
                            <div class="column-content" id="reviewColumn">
                                <!-- Tasks will be populated here -->
                            </div>
                        </div>
                        
                        <div class="kanban-column" data-status="done">
                            <div class="column-header">
                                <h3>Done</h3>
                                <span class="task-count">0</span>
                            </div>
                            <div class="column-content" id="doneColumn">
                                <!-- Tasks will be populated here -->
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Gantt View -->
                <div id="gantt-view" class="task-view">
                    <div class="gantt-container">
                        <div id="ganttChart" class="gantt-chart">
                            <!-- Gantt chart will be rendered here -->
                        </div>
                    </div>
                </div>
                
                <!-- Calendar View -->
                <div id="calendar-view" class="task-view">
                    <div class="calendar-container">
                        <div id="taskCalendar" class="task-calendar">
                            <!-- Calendar will be rendered here -->
                        </div>
                    </div>
                </div>
                
                <!-- List View -->
                <div id="list-view" class="task-view">
                    <div class="task-list-container">
                        <div id="taskList" class="task-list">
                            <!-- Task list will be populated here -->
                        </div>
                    </div>
                </div>

                <!-- Team View -->
                <div id="team-view" class="task-view">
                    <div class="team-container">
                        <div class="team-header">
                            <h3>Team Members</h3>
                            <div class="team-stats">
                                <span id="teamMemberCount">0 members</span>
                            </div>
                        </div>
                        <div id="teamMembersList" class="team-members-grid">
                            <!-- Team members will be populated here -->
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Calendar View -->
        <section id="calendar-view-main" class="view">
            <div class="calendar-header">
                <h1>Calendar</h1>
                <div class="calendar-controls">
                    <button class="btn-secondary" id="prevMonth">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <span id="currentMonth">January 2024</span>
                    <button class="btn-secondary" id="nextMonth">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
            <div id="mainCalendar" class="main-calendar">
                <!-- Main calendar will be rendered here -->
            </div>
        </section>

        <!-- Analytics View -->
        <section id="analytics-view" class="view">
            <div class="analytics-header">
                <h1>Analytics & Reports</h1>
            </div>
            <div class="analytics-content">
                <div class="analytics-grid">
                    <div class="analytics-card">
                        <h3>Project Progress</h3>
                        <div class="chart-container">
                            <canvas id="projectProgressChart"></canvas>
                        </div>
                    </div>
                    <div class="analytics-card">
                        <h3>Task Distribution</h3>
                        <div class="chart-container">
                            <canvas id="taskDistributionChart"></canvas>
                        </div>
                    </div>
                    <div class="analytics-card">
                        <h3>Timeline Overview</h3>
                        <div class="chart-container">
                            <canvas id="timelineChart"></canvas>
                        </div>
                    </div>
                    <div class="analytics-card">
                        <h3>Performance Metrics</h3>
                        <div class="chart-container">
                            <canvas id="performanceMetrics"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Modals -->
    <!-- New Project Modal -->
    <div id="newProjectModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Create New Project</h2>
                <button class="close-btn" data-modal="newProjectModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="newProjectForm">
                    <div class="form-group">
                        <label for="projectName">Project Name</label>
                        <input type="text" id="projectName" required>
                    </div>
                    <div class="form-group">
                        <label for="projectDescription">Description</label>
                        <textarea id="projectDescription" rows="3"></textarea>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="projectStartDate">Start Date</label>
                            <input type="date" id="projectStartDate" required>
                        </div>
                        <div class="form-group">
                            <label for="projectEndDate">End Date</label>
                            <input type="date" id="projectEndDate" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="projectPriority">Priority</label>
                        <select id="projectPriority">
                            <option value="low">Low</option>
                            <option value="medium" selected>Medium</option>
                            <option value="high">High</option>
                            <option value="critical">Critical</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="projectColor">Project Color</label>
                        <div class="color-picker">
                            <input type="color" id="projectColor" value="#3498db">
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-secondary" data-modal="newProjectModal">Cancel</button>
                <button type="submit" form="newProjectForm" class="btn-primary">Create Project</button>
            </div>
        </div>
    </div>

    <!-- New Task Modal -->
    <div id="newTaskModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Create New Task</h2>
                <button class="close-btn" data-modal="newTaskModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="newTaskForm">
                    <div class="form-group">
                        <label for="taskName">Task Name</label>
                        <input type="text" id="taskName" required>
                    </div>
                    <div class="form-group">
                        <label for="taskDescription">Description</label>
                        <textarea id="taskDescription" rows="3"></textarea>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="taskPriority">Priority</label>
                            <select id="taskPriority">
                                <option value="low">Low</option>
                                <option value="medium" selected>Medium</option>
                                <option value="high">High</option>
                                <option value="critical">Critical</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="taskStatus">Status</label>
                            <select id="taskStatus">
                                <option value="todo" selected>To Do</option>
                                <option value="in-progress">In Progress</option>
                                <option value="review">Review</option>
                                <option value="done">Done</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="taskStartDate">Start Date</label>
                            <input type="date" id="taskStartDate">
                        </div>
                        <div class="form-group">
                            <label for="taskDueDate">Due Date</label>
                            <input type="date" id="taskDueDate">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="taskAssignee">Assignee</label>
                        <select id="taskAssignee">
                            <option value="">Select assignee</option>
                            <!-- Team members will be populated here -->
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="taskTags">Tags</label>
                        <input type="text" id="taskTags" placeholder="Enter tags separated by commas">
                    </div>
                    <div class="form-group">
                        <label for="taskEstimate">Time Estimate (hours)</label>
                        <input type="number" id="taskEstimate" min="0" step="0.5" placeholder="0">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-secondary" data-modal="newTaskModal">Cancel</button>
                <button type="submit" form="newTaskForm" class="btn-primary">Create Task</button>
            </div>
        </div>
    </div>

    <!-- Add Team Member Modal -->
    <div id="addTeamMemberModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Add Team Member</h2>
                <button class="close-btn" data-modal="addTeamMemberModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="addTeamMemberForm">
                    <div class="form-group">
                        <label for="memberName">Full Name</label>
                        <input type="text" id="memberName" required>
                    </div>
                    <div class="form-group">
                        <label for="memberEmail">Email</label>
                        <input type="email" id="memberEmail" required>
                    </div>
                    <div class="form-group">
                        <label for="memberRole">Role</label>
                        <select id="memberRole">
                            <option value="developer">Developer</option>
                            <option value="designer">Designer</option>
                            <option value="manager">Project Manager</option>
                            <option value="analyst">Business Analyst</option>
                            <option value="tester">QA Tester</option>
                            <option value="other">Other</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="memberSkills">Skills</label>
                        <input type="text" id="memberSkills" placeholder="Enter skills separated by commas">
                    </div>
                    <div class="form-group">
                        <label for="memberAvatar">Avatar Color</label>
                        <div class="color-picker">
                            <input type="color" id="memberAvatar" value="#4682B4">
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-secondary" data-modal="addTeamMemberModal">Cancel</button>
                <button type="submit" form="addTeamMemberForm" class="btn-primary">Add Member</button>
            </div>
        </div>
    </div>

    <!-- Notifications Dropdown -->
    <div id="notificationsDropdown" class="notifications-dropdown">
        <div class="notifications-header">
            <h3>Notifications</h3>
            <button class="btn-link" id="markAllReadBtn">Mark all as read</button>
        </div>
        <div id="notificationsList" class="notifications-list">
            <!-- Notifications will be populated here -->
        </div>
        <div class="notifications-footer">
            <button class="btn-link">View all notifications</button>
        </div>
    </div>

    <!-- Task Detail Modal -->
    <div id="taskDetailModal" class="modal">
        <div class="modal-content task-detail-modal">
            <div class="modal-header">
                <h2 id="taskDetailTitle">Task Details</h2>
                <div class="task-detail-actions">
                    <button class="btn-secondary" id="editTaskBtn">
                        <i class="fas fa-edit"></i>
                        Edit
                    </button>
                    <button class="btn-secondary delete-task-btn" id="deleteTaskBtn" style="color: #e74c3c; border-color: #e74c3c;">
                        <i class="fas fa-trash"></i>
                        Delete
                    </button>
                    <button class="close-btn" data-modal="taskDetailModal">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <div class="modal-body">
                <div class="task-detail-content">
                    <div class="task-detail-main">
                        <div class="task-detail-section">
                            <h4>Description</h4>
                            <p id="taskDetailDescription">No description</p>
                        </div>

                        <div class="task-detail-section">
                            <h4>Comments</h4>
                            <div id="taskComments" class="task-comments">
                                <!-- Comments will be populated here -->
                            </div>
                            <div class="add-comment">
                                <textarea id="newComment" placeholder="Add a comment..." rows="3"></textarea>
                                <button class="btn-primary" id="addCommentBtn">
                                    <i class="fas fa-comment"></i>
                                    Add Comment
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="task-detail-sidebar">
                        <div class="task-detail-info">
                            <div class="info-item">
                                <label>Status</label>
                                <span id="taskDetailStatus" class="status-badge">To Do</span>
                            </div>
                            <div class="info-item">
                                <label>Priority</label>
                                <span id="taskDetailPriority" class="priority-badge">Medium</span>
                            </div>
                            <div class="info-item">
                                <label>Assignee</label>
                                <div id="taskDetailAssignee" class="assignee-info">
                                    <div class="assignee-avatar">JD</div>
                                    <span>John Doe</span>
                                </div>
                            </div>
                            <div class="info-item">
                                <label>Due Date</label>
                                <span id="taskDetailDueDate">Not set</span>
                            </div>
                            <div class="info-item">
                                <label>Time Estimate</label>
                                <span id="taskDetailEstimate">0 hours</span>
                            </div>
                            <div class="info-item">
                                <label>Time Spent</label>
                                <div class="time-tracking">
                                    <span id="taskDetailTimeSpent">0 hours</span>
                                    <button class="btn-secondary btn-small" id="startTimerBtn">
                                        <i class="fas fa-play"></i>
                                        Start Timer
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="task-detail-tags">
                            <label>Tags</label>
                            <div id="taskDetailTags" class="tags-container">
                                <!-- Tags will be populated here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Help Modal -->
    <div id="helpModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Help & Keyboard Shortcuts</h2>
                <button class="close-btn" data-modal="helpModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="help-section">
                    <h3>Keyboard Shortcuts</h3>
                    <div class="shortcuts-grid">
                        <div class="shortcut-item">
                            <kbd>Ctrl/Cmd + N</kbd>
                            <span>Create New Project</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>Ctrl/Cmd + T</kbd>
                            <span>Create New Task</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>1</kbd>
                            <span>Switch to Dashboard</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>2</kbd>
                            <span>Switch to Projects</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>3</kbd>
                            <span>Switch to Calendar</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>4</kbd>
                            <span>Switch to Analytics</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>Escape</kbd>
                            <span>Close Modals</span>
                        </div>
                    </div>
                </div>

                <div class="help-section">
                    <h3>Features</h3>
                    <ul class="features-list">
                        <li><strong>Drag & Drop:</strong> Move tasks between Kanban columns</li>
                        <li><strong>Time Tracking:</strong> Track time spent on tasks</li>
                        <li><strong>Team Management:</strong> Add and manage team members</li>
                        <li><strong>Comments:</strong> Collaborate with task comments</li>
                        <li><strong>Analytics:</strong> View project progress and metrics</li>
                        <li><strong>Notifications:</strong> Stay updated with real-time alerts</li>
                        <li><strong>Calendar View:</strong> See tasks and deadlines in calendar format</li>
                        <li><strong>Gantt Charts:</strong> Visualize project timelines</li>
                    </ul>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-primary" data-modal="helpModal">Got it!</button>
            </div>
        </div>
    </div>

    <!-- Confirmation Modal -->
    <div id="confirmModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="confirmTitle">Confirm Action</h2>
                <button class="close-btn" data-modal="confirmModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <p id="confirmMessage">Are you sure you want to proceed?</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-secondary" data-modal="confirmModal">Cancel</button>
                <button type="button" class="btn-primary" id="confirmActionBtn">Confirm</button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="app.js"></script>
</body>
</html>
