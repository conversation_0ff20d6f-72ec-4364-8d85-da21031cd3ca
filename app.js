// ProjectFlow - Project Management Application
class ProjectManager {
    constructor() {
        this.projects = JSON.parse(localStorage.getItem('projects')) || [];
        this.currentProject = null;
        this.currentView = 'dashboard';
        this.currentTaskView = 'kanban';
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadDashboard();
        this.updateStats();
    }

    setupEventListeners() {
        // Navigation
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchView(e.target.dataset.view);
            });
        });

        // Project view switchers
        document.querySelectorAll('[data-project-view]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchProjectView(e.target.dataset.projectView);
            });
        });

        // Task view switchers
        document.querySelectorAll('[data-task-view]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchTaskView(e.target.dataset.taskView);
            });
        });

        // Team member management
        document.getElementById('addTeamMemberBtn').addEventListener('click', () => {
            this.openModal('addTeamMemberModal');
        });

        document.getElementById('addTeamMemberForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.addTeamMember();
        });

        // Task detail modal
        document.getElementById('addCommentBtn').addEventListener('click', () => {
            this.addTaskComment();
        });

        document.getElementById('startTimerBtn').addEventListener('click', () => {
            this.toggleTaskTimer();
        });

        // Notifications
        document.getElementById('notificationsBtn').addEventListener('click', () => {
            this.toggleNotifications();
        });

        document.getElementById('markAllReadBtn').addEventListener('click', () => {
            this.markAllNotificationsRead();
        });

        // Help modal
        document.getElementById('helpBtn').addEventListener('click', () => {
            this.openModal('helpModal');
        });

        // Close notifications when clicking outside
        document.addEventListener('click', (e) => {
            const dropdown = document.getElementById('notificationsDropdown');
            const button = document.getElementById('notificationsBtn');
            if (!dropdown.contains(e.target) && !button.contains(e.target)) {
                dropdown.classList.remove('active');
            }
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });

        // Modal controls
        document.getElementById('newProjectBtn').addEventListener('click', () => {
            this.openModal('newProjectModal');
        });

        document.getElementById('newTaskBtn').addEventListener('click', () => {
            this.resetTaskForm();
            this.openModal('newTaskModal');
        });

        document.querySelectorAll('.close-btn, [data-modal]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                if (e.target.dataset.modal) {
                    this.closeModal(e.target.dataset.modal);
                } else {
                    this.closeModal(e.target.closest('.modal').id);
                }
            });
        });

        // Forms
        document.getElementById('newProjectForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.createProject();
        });

        document.getElementById('newTaskForm').addEventListener('submit', (e) => {
            e.preventDefault();
            if (this.editingTaskId) {
                this.updateTask();
            } else {
                this.createTask();
            }
        });

        // Back button
        document.getElementById('backToProjects').addEventListener('click', () => {
            this.switchView('projects');
        });

        // Search and filters
        document.getElementById('searchProjects').addEventListener('input', (e) => {
            this.filterProjects(e.target.value);
        });

        document.getElementById('statusFilter').addEventListener('change', (e) => {
            this.filterProjectsByStatus(e.target.value);
        });

        // Edit task button in task detail modal
        document.getElementById('editTaskBtn').addEventListener('click', () => {
            if (this.currentTask) {
                this.editTask(this.currentTask.id);
            }
        });

        // Delete task button in task detail modal
        document.getElementById('deleteTaskBtn').addEventListener('click', () => {
            if (this.currentTask) {
                this.deleteTask(this.currentTask.id);
            }
        });
    }

    switchView(view) {
        // Update navigation
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-view="${view}"]`).classList.add('active');

        // Update content
        document.querySelectorAll('.view').forEach(v => {
            v.classList.remove('active');
        });

        if (view === 'projects') {
            document.getElementById('projects-view').classList.add('active');
            this.loadProjects();
        } else if (view === 'dashboard') {
            document.getElementById('dashboard-view').classList.add('active');
            this.loadDashboard();
        } else if (view === 'calendar') {
            document.getElementById('calendar-view-main').classList.add('active');
            this.loadCalendar();
        } else if (view === 'analytics') {
            document.getElementById('analytics-view').classList.add('active');
            this.loadAnalytics();
        }

        this.currentView = view;
    }

    switchProjectView(view) {
        document.querySelectorAll('[data-project-view]').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-project-view="${view}"]`).classList.add('active');

        const container = document.getElementById('projectsContainer');
        container.className = `projects-container ${view}-view`;
    }

    switchTaskView(view) {
        document.querySelectorAll('[data-task-view]').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-task-view="${view}"]`).classList.add('active');

        document.querySelectorAll('.task-view').forEach(v => {
            v.classList.remove('active');
        });

        if (view === 'kanban') {
            document.getElementById('kanban-view').classList.add('active');
            this.loadKanbanBoard();
        } else if (view === 'gantt') {
            document.getElementById('gantt-view').classList.add('active');
            this.loadGanttChart();
        } else if (view === 'calendar') {
            document.getElementById('calendar-view').classList.add('active');
            this.loadTaskCalendar();
        } else if (view === 'list') {
            document.getElementById('list-view').classList.add('active');
            this.loadTaskList();
        } else if (view === 'team') {
            document.getElementById('team-view').classList.add('active');
            this.loadTeamView();
        }

        this.currentTaskView = view;
    }

    openModal(modalId) {
        document.getElementById(modalId).classList.add('active');
        document.body.style.overflow = 'hidden';
    }

    closeModal(modalId) {
        document.getElementById(modalId).classList.remove('active');
        document.body.style.overflow = 'auto';

        // Reset task form when closing the task modal
        if (modalId === 'newTaskModal') {
            this.resetTaskForm();
        }
    }

    createProject() {
        const form = document.getElementById('newProjectForm');
        const formData = new FormData(form);
        
        const project = {
            id: this.generateId(),
            name: document.getElementById('projectName').value,
            description: document.getElementById('projectDescription').value,
            startDate: document.getElementById('projectStartDate').value,
            endDate: document.getElementById('projectEndDate').value,
            priority: document.getElementById('projectPriority').value,
            color: document.getElementById('projectColor').value,
            status: 'active',
            progress: 0,
            tasks: [],
            createdAt: new Date().toISOString()
        };

        this.projects.push(project);
        this.saveProjects();
        this.closeModal('newProjectModal');
        form.reset();
        
        if (this.currentView === 'projects') {
            this.loadProjects();
        }
        this.updateStats();
        this.showNotification('Project created successfully!', 'success');
    }

    createTask() {
        if (!this.currentProject) return;

        const assigneeSelect = document.getElementById('taskAssignee');
        const assigneeName = assigneeSelect.options[assigneeSelect.selectedIndex]?.text || '';

        const task = {
            id: this.generateId(),
            name: document.getElementById('taskName').value,
            description: document.getElementById('taskDescription').value,
            priority: document.getElementById('taskPriority').value,
            status: document.getElementById('taskStatus').value,
            startDate: document.getElementById('taskStartDate').value,
            dueDate: document.getElementById('taskDueDate').value,
            assignee: assigneeName,
            assigneeId: document.getElementById('taskAssignee').value,
            tags: document.getElementById('taskTags').value.split(',').map(tag => tag.trim()).filter(tag => tag),
            estimate: parseFloat(document.getElementById('taskEstimate').value) || 0,
            timeSpent: 0,
            comments: [],
            createdAt: new Date().toISOString(),
            completedAt: null
        };

        this.currentProject.tasks.push(task);
        this.saveProjects();
        this.closeModal('newTaskModal');
        document.getElementById('newTaskForm').reset();

        this.loadKanbanBoard();
        this.updateProjectProgress();
        this.updateStats();
        this.showNotification('Task created successfully!', 'success');
    }

    editTask(taskId) {
        const task = this.currentProject.tasks.find(t => t.id === taskId);
        if (!task) return;

        // Populate the form with existing task data
        document.getElementById('taskName').value = task.name;
        document.getElementById('taskDescription').value = task.description || '';
        document.getElementById('taskPriority').value = task.priority;
        document.getElementById('taskStatus').value = task.status;
        document.getElementById('taskStartDate').value = task.startDate || '';
        document.getElementById('taskDueDate').value = task.dueDate || '';
        document.getElementById('taskAssignee').value = task.assigneeId || '';
        document.getElementById('taskTags').value = task.tags ? task.tags.join(', ') : '';
        document.getElementById('taskEstimate').value = task.estimate || '';

        // Change the form to edit mode
        this.editingTaskId = taskId;
        document.querySelector('#newTaskModal .modal-header h2').textContent = 'Edit Task';
        document.querySelector('#newTaskModal .btn-primary').textContent = 'Update Task';

        // Close task detail modal if open
        this.closeModal('taskDetailModal');

        // Open the task form modal
        this.openModal('newTaskModal');
    }

    deleteTask(taskId) {
        if (!this.currentProject) return;

        const task = this.currentProject.tasks.find(t => t.id === taskId);
        if (!task) return;

        // Show custom confirmation dialog
        this.showConfirmation(
            'Delete Task',
            `Are you sure you want to delete the task "${task.name}"? This action cannot be undone.`,
            () => {
                // Remove task from project
                this.currentProject.tasks = this.currentProject.tasks.filter(t => t.id !== taskId);

                this.saveProjects();
                this.loadKanbanBoard();
                this.updateProjectProgress();
                this.updateStats();
                this.showNotification('Task deleted successfully!', 'success');

                // Close task detail modal if it's open for this task
                if (this.currentTask && this.currentTask.id === taskId) {
                    this.closeModal('taskDetailModal');
                }
            }
        );
    }

    showConfirmation(title, message, onConfirm) {
        document.getElementById('confirmTitle').textContent = title;
        document.getElementById('confirmMessage').textContent = message;

        const confirmBtn = document.getElementById('confirmActionBtn');

        // Remove existing event listeners
        const newConfirmBtn = confirmBtn.cloneNode(true);
        confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);

        // Add new event listener
        newConfirmBtn.addEventListener('click', () => {
            this.closeModal('confirmModal');
            onConfirm();
        });

        this.openModal('confirmModal');
    }

    updateTask() {
        if (!this.currentProject || !this.editingTaskId) return;

        const task = this.currentProject.tasks.find(t => t.id === this.editingTaskId);
        if (!task) return;

        const assigneeSelect = document.getElementById('taskAssignee');
        const assigneeName = assigneeSelect.options[assigneeSelect.selectedIndex]?.text || '';

        // Update task properties
        task.name = document.getElementById('taskName').value;
        task.description = document.getElementById('taskDescription').value;
        task.priority = document.getElementById('taskPriority').value;
        task.status = document.getElementById('taskStatus').value;
        task.startDate = document.getElementById('taskStartDate').value;
        task.dueDate = document.getElementById('taskDueDate').value;
        task.assignee = assigneeName;
        task.assigneeId = document.getElementById('taskAssignee').value;
        task.tags = document.getElementById('taskTags').value.split(',').map(tag => tag.trim()).filter(tag => tag);
        task.estimate = parseFloat(document.getElementById('taskEstimate').value) || 0;

        this.saveProjects();
        this.closeModal('newTaskModal');
        this.resetTaskForm();

        this.loadKanbanBoard();
        this.updateProjectProgress();
        this.updateStats();
        this.showNotification('Task updated successfully!', 'success');
    }

    resetTaskForm() {
        document.getElementById('newTaskForm').reset();
        this.editingTaskId = null;
        document.querySelector('#newTaskModal .modal-header h2').textContent = 'Create New Task';
        document.querySelector('#newTaskModal .btn-primary').textContent = 'Create Task';
    }

    addTeamMember() {
        if (!this.currentProject) return;

        if (!this.currentProject.teamMembers) {
            this.currentProject.teamMembers = [];
        }

        const member = {
            id: this.generateId(),
            name: document.getElementById('memberName').value,
            email: document.getElementById('memberEmail').value,
            role: document.getElementById('memberRole').value,
            skills: document.getElementById('memberSkills').value.split(',').map(skill => skill.trim()).filter(skill => skill),
            avatar: document.getElementById('memberAvatar').value,
            joinedAt: new Date().toISOString(),
            tasksAssigned: 0,
            tasksCompleted: 0
        };

        this.currentProject.teamMembers.push(member);
        this.saveProjects();
        this.closeModal('addTeamMemberModal');
        document.getElementById('addTeamMemberForm').reset();

        this.loadTeamView();
        this.updateTaskAssigneeOptions();
        this.showNotification('Team member added successfully!', 'success');
    }

    updateTaskAssigneeOptions() {
        const assigneeSelect = document.getElementById('taskAssignee');
        assigneeSelect.innerHTML = '<option value="">Select assignee</option>';

        if (this.currentProject && this.currentProject.teamMembers) {
            this.currentProject.teamMembers.forEach(member => {
                const option = document.createElement('option');
                option.value = member.id;
                option.textContent = member.name;
                assigneeSelect.appendChild(option);
            });
        }
    }

    loadTeamView() {
        if (!this.currentProject) return;

        const container = document.getElementById('teamMembersList');
        const teamMembers = this.currentProject.teamMembers || [];

        document.getElementById('teamMemberCount').textContent = `${teamMembers.length} member${teamMembers.length !== 1 ? 's' : ''}`;

        if (teamMembers.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-users"></i>
                    <h3>No team members yet</h3>
                    <p>Add team members to start collaborating</p>
                    <button class="btn-primary" onclick="projectManager.openModal('addTeamMemberModal')">
                        <i class="fas fa-user-plus"></i>
                        Add Team Member
                    </button>
                </div>
            `;
            return;
        }

        // Calculate member statistics
        teamMembers.forEach(member => {
            member.tasksAssigned = this.currentProject.tasks.filter(task => task.assigneeId === member.id).length;
            member.tasksCompleted = this.currentProject.tasks.filter(task => task.assigneeId === member.id && task.status === 'done').length;
        });

        container.innerHTML = teamMembers.map(member => `
            <div class="team-member-card">
                <div class="team-member-header">
                    <div class="team-member-avatar" style="background-color: ${member.avatar}">
                        ${member.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                    </div>
                    <div class="team-member-info">
                        <h4>${member.name}</h4>
                        <div class="team-member-role">${member.role}</div>
                    </div>
                </div>
                <div class="team-member-email">${member.email}</div>
                <div class="team-member-skills">
                    ${member.skills.map(skill => `<span class="skill-tag">${skill}</span>`).join('')}
                </div>
                <div class="team-member-stats">
                    <div class="stat-item">
                        <span class="stat-value">${member.tasksAssigned}</span>
                        <span class="stat-label">Assigned</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value">${member.tasksCompleted}</span>
                        <span class="stat-label">Completed</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value">${member.tasksAssigned > 0 ? Math.round((member.tasksCompleted / member.tasksAssigned) * 100) : 0}%</span>
                        <span class="stat-label">Success Rate</span>
                    </div>
                </div>
            </div>
        `).join('');
    }

    loadDashboard() {
        this.loadRecentProjects();
        this.loadProgressChart();
        this.loadUpcomingDeadlines();
        this.loadActivityFeed();
        this.updateStats();
    }

    loadRecentProjects() {
        const container = document.getElementById('recentProjectsList');
        const recentProjects = this.projects
            .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
            .slice(0, 5);

        if (recentProjects.length === 0) {
            container.innerHTML = '<p class="empty-state">No projects yet. Create your first project!</p>';
            return;
        }

        container.innerHTML = recentProjects.map(project => `
            <div class="project-card" onclick="projectManager.openProject('${project.id}')">
                <div class="project-card-header">
                    <div class="project-title">${project.name}</div>
                    <span class="status-badge ${project.status}">${project.status}</span>
                </div>
                <div class="project-description">${project.description || 'No description'}</div>
                <div class="project-progress">
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${project.progress}%"></div>
                    </div>
                    <span class="progress-text">${project.progress}% Complete</span>
                </div>
                <div class="project-meta">
                    <span>${project.tasks.length} tasks</span>
                    <span>Due: ${this.formatDate(project.endDate)}</span>
                </div>
            </div>
        `).join('');
    }

    loadProjects() {
        const container = document.getElementById('projectsContainer');
        
        if (this.projects.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-folder-open"></i>
                    <h3>No projects yet</h3>
                    <p>Create your first project to get started</p>
                    <button class="btn-primary" onclick="projectManager.openModal('newProjectModal')">
                        <i class="fas fa-plus"></i>
                        Create Project
                    </button>
                </div>
            `;
            return;
        }

        container.innerHTML = this.projects.map(project => `
            <div class="project-card" onclick="projectManager.openProject('${project.id}')" style="border-left-color: ${project.color}">
                <div class="project-card-header">
                    <div class="project-title">${project.name}</div>
                    <span class="status-badge ${project.status}">${project.status}</span>
                </div>
                <div class="project-description">${project.description || 'No description'}</div>
                <div class="project-progress">
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${project.progress}%; background: ${project.color}"></div>
                    </div>
                    <span class="progress-text">${project.progress}% Complete</span>
                </div>
                <div class="project-meta">
                    <span><i class="fas fa-tasks"></i> ${project.tasks.length} tasks</span>
                    <span><i class="fas fa-calendar"></i> ${this.formatDate(project.endDate)}</span>
                </div>
            </div>
        `).join('');
    }

    openProject(projectId) {
        this.currentProject = this.projects.find(p => p.id === projectId);
        if (!this.currentProject) return;

        // Initialize team members if not exists
        if (!this.currentProject.teamMembers) {
            this.currentProject.teamMembers = this.getDefaultTeamMembers();
            this.saveProjects();
        }

        document.getElementById('projectTitle').textContent = this.currentProject.name;
        document.getElementById('projectStatus').textContent = this.currentProject.status;
        document.getElementById('projectStatus').className = `status-badge ${this.currentProject.status}`;

        document.getElementById('project-detail-view').classList.add('active');
        document.getElementById('projects-view').classList.remove('active');

        this.updateTaskAssigneeOptions();
        this.loadKanbanBoard();
    }

    getDefaultTeamMembers() {
        return [
            {
                id: 'member-1',
                name: 'John Doe',
                email: '<EMAIL>',
                role: 'developer',
                skills: ['JavaScript', 'React', 'Node.js'],
                avatar: '#4682B4',
                joinedAt: new Date().toISOString(),
                tasksAssigned: 0,
                tasksCompleted: 0
            },
            {
                id: 'member-2',
                name: 'Jane Smith',
                email: '<EMAIL>',
                role: 'designer',
                skills: ['UI/UX', 'Figma', 'Adobe Creative Suite'],
                avatar: '#FFD700',
                joinedAt: new Date().toISOString(),
                tasksAssigned: 0,
                tasksCompleted: 0
            },
            {
                id: 'member-3',
                name: 'Bob Johnson',
                email: '<EMAIL>',
                role: 'manager',
                skills: ['Project Management', 'Agile', 'Leadership'],
                avatar: '#87CEEB',
                joinedAt: new Date().toISOString(),
                tasksAssigned: 0,
                tasksCompleted: 0
            }
        ];
    }

    loadKanbanBoard() {
        if (!this.currentProject) return;

        const columns = {
            'todo': document.getElementById('todoColumn'),
            'in-progress': document.getElementById('inProgressColumn'),
            'review': document.getElementById('reviewColumn'),
            'done': document.getElementById('doneColumn')
        };

        // Clear columns
        Object.values(columns).forEach(column => column.innerHTML = '');

        // Group tasks by status
        const tasksByStatus = this.currentProject.tasks.reduce((acc, task) => {
            if (!acc[task.status]) acc[task.status] = [];
            acc[task.status].push(task);
            return acc;
        }, {});

        // Populate columns
        Object.entries(tasksByStatus).forEach(([status, tasks]) => {
            if (columns[status]) {
                columns[status].innerHTML = tasks.map(task => this.createTaskCard(task)).join('');
            }
        });

        // Update task counts
        Object.entries(columns).forEach(([status, column]) => {
            const count = tasksByStatus[status]?.length || 0;
            const header = column.closest('.kanban-column').querySelector('.task-count');
            header.textContent = count;
        });

        // Setup drag and drop after a small delay to ensure DOM is ready
        setTimeout(() => {
            this.setupDragAndDrop();
            console.log('Drag and drop setup completed');
        }, 100);
    }

    createTaskCard(task) {
        const isOverdue = task.dueDate && new Date(task.dueDate) < new Date() && task.status !== 'done';
        const assignee = this.getTaskAssignee(task);

        return `
            <div class="task-card" draggable="true" data-task-id="${task.id}" ${isOverdue ? 'style="border-left-color: #e74c3c"' : ''}>
                <div class="task-header">
                    <div class="task-title" onclick="event.stopPropagation(); projectManager.openTaskDetail('${task.id}')">${task.name}</div>
                    <div class="task-actions">
                        <button class="task-action-btn edit-btn" onclick="event.stopPropagation(); projectManager.editTask('${task.id}')" title="Edit Task">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="task-action-btn delete-btn" onclick="event.stopPropagation(); projectManager.deleteTask('${task.id}')" title="Delete Task">
                            <i class="fas fa-trash"></i>
                        </button>
                        <span class="priority-badge ${task.priority}">${task.priority}</span>
                    </div>
                </div>
                <div class="task-description" onclick="event.stopPropagation(); projectManager.openTaskDetail('${task.id}')">${task.description || 'No description'}</div>
                ${task.tags && task.tags.length > 0 ? `
                    <div class="task-tags" onclick="event.stopPropagation(); projectManager.openTaskDetail('${task.id}')">
                        ${task.tags.map(tag => `<span class="task-tag">${tag}</span>`).join('')}
                    </div>
                ` : ''}
                <div class="task-meta" onclick="event.stopPropagation(); projectManager.openTaskDetail('${task.id}')">
                    ${task.estimate ? `<span class="task-estimate"><i class="fas fa-clock"></i> ${task.estimate}h</span>` : ''}
                    ${task.dueDate ? `<span class="due-date ${isOverdue ? 'overdue' : ''}">${this.formatDate(task.dueDate)}</span>` : ''}
                </div>
                ${assignee ? `
                    <div class="task-assignee" onclick="event.stopPropagation(); projectManager.openTaskDetail('${task.id}')">
                        <div class="assignee-avatar" style="background-color: ${assignee.avatar}">
                            ${assignee.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                        </div>
                        <span class="assignee-name">${assignee.name}</span>
                    </div>
                ` : ''}
            </div>
        `;
    }

    openTaskDetail(taskId) {
        const task = this.currentProject.tasks.find(t => t.id === taskId);
        if (!task) return;

        this.currentTask = task;

        // Populate task details
        document.getElementById('taskDetailTitle').textContent = task.name;
        document.getElementById('taskDetailDescription').textContent = task.description || 'No description';
        document.getElementById('taskDetailStatus').textContent = task.status;
        document.getElementById('taskDetailStatus').className = `status-badge ${task.status}`;
        document.getElementById('taskDetailPriority').textContent = task.priority;
        document.getElementById('taskDetailPriority').className = `priority-badge ${task.priority}`;
        document.getElementById('taskDetailDueDate').textContent = task.dueDate ? this.formatDate(task.dueDate) : 'Not set';
        document.getElementById('taskDetailEstimate').textContent = task.estimate ? `${task.estimate} hours` : '0 hours';
        document.getElementById('taskDetailTimeSpent').textContent = task.timeSpent ? `${task.timeSpent} hours` : '0 hours';

        // Populate assignee
        const assignee = this.getTaskAssignee(task);
        const assigneeContainer = document.getElementById('taskDetailAssignee');
        if (assignee) {
            assigneeContainer.innerHTML = `
                <div class="assignee-avatar" style="background-color: ${assignee.avatar}">
                    ${assignee.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                </div>
                <span>${assignee.name}</span>
            `;
        } else {
            assigneeContainer.innerHTML = '<span>Unassigned</span>';
        }

        // Populate tags
        const tagsContainer = document.getElementById('taskDetailTags');
        if (task.tags && task.tags.length > 0) {
            tagsContainer.innerHTML = task.tags.map(tag => `<span class="task-tag">${tag}</span>`).join('');
        } else {
            tagsContainer.innerHTML = '<span class="no-tags">No tags</span>';
        }

        // Load comments
        this.loadTaskComments(task);

        // Open modal
        this.openModal('taskDetailModal');
    }

    loadTaskComments(task) {
        const container = document.getElementById('taskComments');
        const comments = task.comments || [];

        if (comments.length === 0) {
            container.innerHTML = '<p class="empty-state">No comments yet</p>';
            return;
        }

        container.innerHTML = comments.map(comment => `
            <div class="comment-item">
                <div class="comment-header">
                    <span class="comment-author">${comment.author}</span>
                    <span class="comment-date">${this.formatDate(comment.createdAt)}</span>
                </div>
                <div class="comment-text">${comment.text}</div>
            </div>
        `).join('');
    }

    addTaskComment() {
        const commentText = document.getElementById('newComment').value.trim();
        if (!commentText || !this.currentTask) return;

        if (!this.currentTask.comments) {
            this.currentTask.comments = [];
        }

        const comment = {
            id: this.generateId(),
            text: commentText,
            author: 'Current User', // In a real app, this would be the logged-in user
            createdAt: new Date().toISOString()
        };

        this.currentTask.comments.push(comment);
        this.saveProjects();

        document.getElementById('newComment').value = '';
        this.loadTaskComments(this.currentTask);
        this.showNotification('Comment added successfully!', 'success');
    }

    toggleTaskTimer() {
        if (!this.currentTask) return;

        const button = document.getElementById('startTimerBtn');
        const icon = button.querySelector('i');

        if (this.taskTimer) {
            // Stop timer
            clearInterval(this.taskTimer);
            this.taskTimer = null;
            button.innerHTML = '<i class="fas fa-play"></i> Start Timer';
            button.classList.remove('btn-primary');
            button.classList.add('btn-secondary');
            this.showNotification('Timer stopped', 'info');
        } else {
            // Start timer
            this.taskTimerStart = Date.now();
            this.taskTimer = setInterval(() => {
                const elapsed = (Date.now() - this.taskTimerStart) / (1000 * 60 * 60); // Convert to hours
                const currentTimeSpent = this.currentTask.timeSpent || 0;
                document.getElementById('taskDetailTimeSpent').textContent = `${(currentTimeSpent + elapsed).toFixed(2)} hours`;
            }, 1000);

            button.innerHTML = '<i class="fas fa-stop"></i> Stop Timer';
            button.classList.remove('btn-secondary');
            button.classList.add('btn-primary');
            this.showNotification('Timer started', 'success');
        }
    }

    toggleNotifications() {
        const dropdown = document.getElementById('notificationsDropdown');
        dropdown.classList.toggle('active');

        if (dropdown.classList.contains('active')) {
            this.loadNotifications();
        }
    }

    loadNotifications() {
        const container = document.getElementById('notificationsList');
        const notifications = this.getNotifications();

        if (notifications.length === 0) {
            container.innerHTML = '<div class="notification-item"><p class="empty-state">No notifications</p></div>';
            return;
        }

        container.innerHTML = notifications.map(notification => `
            <div class="notification-item ${notification.read ? '' : 'unread'}">
                <div class="notification-content">
                    <div class="notification-icon" style="background-color: ${notification.color}">
                        <i class="fas fa-${notification.icon}"></i>
                    </div>
                    <div class="notification-text">
                        <div class="notification-title">${notification.title}</div>
                        <div class="notification-message">${notification.message}</div>
                        <div class="notification-time">${this.getTimeAgo(notification.createdAt)}</div>
                    </div>
                </div>
            </div>
        `).join('');

        // Update notification count
        const unreadCount = notifications.filter(n => !n.read).length;
        const badge = document.getElementById('notificationCount');
        if (unreadCount > 0) {
            badge.textContent = unreadCount;
            badge.style.display = 'flex';
        } else {
            badge.style.display = 'none';
        }
    }

    getNotifications() {
        // In a real app, this would come from a server
        return [
            {
                id: 1,
                title: 'Task Completed',
                message: 'John Doe completed "Design Mockups" in Website Redesign',
                icon: 'check-circle',
                color: '#4682B4',
                read: false,
                createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000) // 2 hours ago
            },
            {
                id: 2,
                title: 'New Comment',
                message: 'Jane Smith added a comment to "Frontend Development"',
                icon: 'comment',
                color: '#FFD700',
                read: false,
                createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000) // 4 hours ago
            },
            {
                id: 3,
                title: 'Deadline Approaching',
                message: 'Task "Content Migration" is due tomorrow',
                icon: 'clock',
                color: '#FFA500',
                read: false,
                createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000) // 6 hours ago
            }
        ];
    }

    markAllNotificationsRead() {
        // In a real app, this would update the server
        document.getElementById('notificationCount').style.display = 'none';
        document.querySelectorAll('.notification-item').forEach(item => {
            item.classList.remove('unread');
        });
        this.showNotification('All notifications marked as read', 'success');
    }

    loadActivityFeed() {
        const container = document.getElementById('activityFeed');
        const activities = this.getRecentActivities();

        if (activities.length === 0) {
            container.innerHTML = '<p class="empty-state">No recent activity</p>';
            return;
        }

        container.innerHTML = activities.map(activity => `
            <div class="activity-item">
                <div class="activity-icon" style="background-color: ${activity.color}">
                    <i class="fas fa-${activity.icon}"></i>
                </div>
                <div class="activity-content">
                    <div class="activity-text">
                        <span class="activity-user">${activity.user}</span> ${activity.action}
                    </div>
                    <div class="activity-time">${this.getTimeAgo(activity.createdAt)}</div>
                </div>
            </div>
        `).join('');
    }

    getRecentActivities() {
        // In a real app, this would come from a server
        return [
            {
                id: 1,
                user: 'John Doe',
                action: 'completed task "Design Mockups"',
                icon: 'check',
                color: '#4682B4',
                createdAt: new Date(Date.now() - 1 * 60 * 60 * 1000) // 1 hour ago
            },
            {
                id: 2,
                user: 'Jane Smith',
                action: 'started working on "Frontend Development"',
                icon: 'play',
                color: '#FFD700',
                createdAt: new Date(Date.now() - 3 * 60 * 60 * 1000) // 3 hours ago
            },
            {
                id: 3,
                user: 'Bob Johnson',
                action: 'created new task "Content Migration"',
                icon: 'plus',
                color: '#87CEEB',
                createdAt: new Date(Date.now() - 5 * 60 * 60 * 1000) // 5 hours ago
            },
            {
                id: 4,
                user: 'Alice Wilson',
                action: 'joined the project team',
                icon: 'user-plus',
                color: '#FFA500',
                createdAt: new Date(Date.now() - 8 * 60 * 60 * 1000) // 8 hours ago
            }
        ];
    }

    getTimeAgo(date) {
        const now = new Date();
        const diffInMinutes = Math.floor((now - date) / (1000 * 60));

        if (diffInMinutes < 1) return 'Just now';
        if (diffInMinutes < 60) return `${diffInMinutes}m ago`;

        const diffInHours = Math.floor(diffInMinutes / 60);
        if (diffInHours < 24) return `${diffInHours}h ago`;

        const diffInDays = Math.floor(diffInHours / 24);
        return `${diffInDays}d ago`;
    }

    handleKeyboardShortcuts(e) {
        // Ctrl/Cmd + N: New Project
        if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
            e.preventDefault();
            this.openModal('newProjectModal');
        }

        // Ctrl/Cmd + T: New Task (when in project view)
        if ((e.ctrlKey || e.metaKey) && e.key === 't' && this.currentProject) {
            e.preventDefault();
            this.openModal('newTaskModal');
        }

        // Escape: Close modals
        if (e.key === 'Escape') {
            document.querySelectorAll('.modal.active').forEach(modal => {
                modal.classList.remove('active');
            });
            document.body.style.overflow = 'auto';
        }

        // Number keys 1-4: Switch main views
        if (e.key >= '1' && e.key <= '4' && !e.ctrlKey && !e.metaKey && !e.target.matches('input, textarea')) {
            const views = ['dashboard', 'projects', 'calendar', 'analytics'];
            const viewIndex = parseInt(e.key) - 1;
            if (views[viewIndex]) {
                this.switchView(views[viewIndex]);
            }
        }
    }

    getTaskAssignee(task) {
        if (!task.assigneeId || !this.currentProject.teamMembers) return null;
        return this.currentProject.teamMembers.find(member => member.id === task.assigneeId);
    }

    setupDragAndDrop() {
        // Clear any existing drag data
        this.draggedTaskId = null;

        // Remove existing event listeners from kanban board
        const kanbanBoard = document.querySelector('.kanban-board');
        if (kanbanBoard) {
            // Clone the board to remove all event listeners
            const newBoard = kanbanBoard.cloneNode(true);
            kanbanBoard.parentNode.replaceChild(newBoard, kanbanBoard);
        }

        // Use event delegation on the kanban board
        const board = document.querySelector('.kanban-board');
        if (board) {
            board.addEventListener('dragstart', (e) => {
                if (e.target.classList.contains('task-card')) {
                    this.onDragStart(e);
                }
            });

            board.addEventListener('dragend', (e) => {
                if (e.target.classList.contains('task-card')) {
                    this.onDragEnd(e);
                }
            });

            board.addEventListener('dragover', (e) => {
                if (e.target.classList.contains('column-content') || e.target.closest('.column-content')) {
                    this.onDragOver(e);
                }
            });

            board.addEventListener('dragleave', (e) => {
                if (e.target.classList.contains('column-content') || e.target.closest('.column-content')) {
                    this.onDragLeave(e);
                }
            });

            board.addEventListener('drop', (e) => {
                if (e.target.classList.contains('column-content') || e.target.closest('.column-content')) {
                    this.onDrop(e);
                }
            });

            console.log('Drag and drop event delegation setup on kanban board');
        }
    }

    onDragStart(e) {
        this.draggedTaskId = e.target.dataset.taskId;
        e.target.classList.add('dragging');
        e.dataTransfer.effectAllowed = 'move';
        e.dataTransfer.setData('text/html', e.target.outerHTML);
        e.dataTransfer.setData('text/plain', this.draggedTaskId);

        console.log('Drag started for task:', this.draggedTaskId);
    }

    onDragEnd(e) {
        e.target.classList.remove('dragging');
        // Clean up all drop zones
        document.querySelectorAll('.column-content').forEach(col => {
            col.classList.remove('drop-zone');
        });
        this.draggedTaskId = null;

        console.log('Drag ended');
    }

    onDragOver(e) {
        e.preventDefault();
        e.dataTransfer.dropEffect = 'move';

        // Find the column content element
        const columnContent = e.target.classList.contains('column-content') ?
            e.target : e.target.closest('.column-content');

        if (columnContent) {
            columnContent.classList.add('drop-zone');
        }
    }

    onDragLeave(e) {
        // Find the column content element
        const columnContent = e.target.classList.contains('column-content') ?
            e.target : e.target.closest('.column-content');

        if (columnContent) {
            // Only remove drop-zone if we're leaving the column entirely
            const rect = columnContent.getBoundingClientRect();
            const x = e.clientX;
            const y = e.clientY;

            if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
                columnContent.classList.remove('drop-zone');
            }
        }
    }

    onDrop(e) {
        e.preventDefault();

        // Find the column content element
        const columnContent = e.target.classList.contains('column-content') ?
            e.target : e.target.closest('.column-content');

        if (columnContent) {
            columnContent.classList.remove('drop-zone');

            const taskId = e.dataTransfer.getData('text/plain') || this.draggedTaskId;
            const kanbanColumn = columnContent.closest('.kanban-column');
            const newStatus = kanbanColumn ? kanbanColumn.dataset.status : null;

            console.log('Drop event - Task ID:', taskId, 'New Status:', newStatus);

            if (taskId && newStatus) {
                this.updateTaskStatus(taskId, newStatus);
            }
        }
    }

    updateTaskStatus(taskId, newStatus) {
        console.log('Updating task status:', taskId, 'to', newStatus);

        const task = this.currentProject.tasks.find(t => t.id === taskId);
        if (!task) {
            console.error('Task not found:', taskId);
            return;
        }

        const oldStatus = task.status;
        task.status = newStatus;

        if (newStatus === 'done' && !task.completedAt) {
            task.completedAt = new Date().toISOString();
        } else if (newStatus !== 'done') {
            task.completedAt = null;
        }

        console.log('Task status updated from', oldStatus, 'to', newStatus);

        this.saveProjects();
        this.loadKanbanBoard();
        this.updateProjectProgress();
        this.updateStats();

        this.showNotification(`Task "${task.name}" moved to ${newStatus.replace('-', ' ')}`, 'success');
    }

    // Debug function to test drag and drop
    testDragAndDrop() {
        console.log('Testing drag and drop setup...');
        const taskCards = document.querySelectorAll('.task-card');
        const columns = document.querySelectorAll('.column-content');

        console.log('Found task cards:', taskCards.length);
        console.log('Found columns:', columns.length);

        taskCards.forEach((card, index) => {
            console.log(`Task card ${index}:`, card.dataset.taskId, 'draggable:', card.draggable);
        });

        if (taskCards.length > 0 && columns.length > 0) {
            console.log('✅ Drag and drop elements found');
            return true;
        } else {
            console.log('❌ Drag and drop elements missing');
            return false;
        }
    }

    updateProjectProgress() {
        if (!this.currentProject) return;

        const totalTasks = this.currentProject.tasks.length;
        const completedTasks = this.currentProject.tasks.filter(t => t.status === 'done').length;
        
        this.currentProject.progress = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;
        
        if (this.currentProject.progress === 100 && this.currentProject.status === 'active') {
            this.currentProject.status = 'completed';
        }

        this.saveProjects();
    }

    updateStats() {
        const totalProjects = this.projects.length;
        const totalTasks = this.projects.reduce((sum, p) => sum + p.tasks.length, 0);
        const completedTasks = this.projects.reduce((sum, p) => 
            sum + p.tasks.filter(t => t.status === 'done').length, 0);
        const overdueTasks = this.projects.reduce((sum, p) => 
            sum + p.tasks.filter(t => 
                t.dueDate && new Date(t.dueDate) < new Date() && t.status !== 'done'
            ).length, 0);

        document.getElementById('totalProjects').textContent = totalProjects;
        document.getElementById('totalTasks').textContent = totalTasks;
        document.getElementById('completedTasks').textContent = completedTasks;
        document.getElementById('overdueTasks').textContent = overdueTasks;
    }

    loadProgressChart() {
        const container = document.getElementById('progressChart');
        const projects = this.projects.slice(0, 5); // Show top 5 projects
        
        if (projects.length === 0) {
            container.innerHTML = '<p class="empty-state">No projects to display</p>';
            return;
        }

        // Create SVG progress chart
        const svg = this.createProgressSVG(projects);
        container.innerHTML = svg;
    }

    createProgressSVG(projects) {
        const width = 400;
        const height = 200;
        const barHeight = 25;
        const spacing = 35;
        
        let svg = `<svg width="${width}" height="${height}" viewBox="0 0 ${width} ${height}">`;
        
        projects.forEach((project, index) => {
            const y = index * spacing + 10;
            const progressWidth = (project.progress / 100) * 300;
            
            svg += `
                <rect x="10" y="${y}" width="300" height="${barHeight}" fill="#ecf0f1" rx="12"/>
                <rect x="10" y="${y}" width="${progressWidth}" height="${barHeight}" fill="${project.color}" rx="12"/>
                <text x="320" y="${y + 17}" font-size="12" fill="#2c3e50">${project.name} (${project.progress}%)</text>
            `;
        });
        
        svg += '</svg>';
        return svg;
    }

    loadUpcomingDeadlines() {
        const container = document.getElementById('deadlinesList');
        const allTasks = this.projects.flatMap(p => 
            p.tasks.map(t => ({...t, projectName: p.name, projectColor: p.color}))
        );
        
        const upcomingTasks = allTasks
            .filter(t => t.dueDate && t.status !== 'done')
            .sort((a, b) => new Date(a.dueDate) - new Date(b.dueDate))
            .slice(0, 5);

        if (upcomingTasks.length === 0) {
            container.innerHTML = '<p class="empty-state">No upcoming deadlines</p>';
            return;
        }

        container.innerHTML = upcomingTasks.map(task => {
            const isOverdue = new Date(task.dueDate) < new Date();
            const daysUntilDue = Math.ceil((new Date(task.dueDate) - new Date()) / (1000 * 60 * 60 * 24));
            
            return `
                <div class="deadline-item ${isOverdue ? 'overdue' : ''}">
                    <div class="deadline-info">
                        <div class="task-name">${task.name}</div>
                        <div class="project-name" style="color: ${task.projectColor}">${task.projectName}</div>
                    </div>
                    <div class="deadline-date">
                        ${isOverdue ? 
                            `<span class="overdue-text">Overdue by ${Math.abs(daysUntilDue)} days</span>` :
                            `<span class="due-text">${daysUntilDue === 0 ? 'Due today' : `${daysUntilDue} days left`}</span>`
                        }
                    </div>
                </div>
            `;
        }).join('');
    }

    loadGanttChart() {
        const container = document.getElementById('ganttChart');
        if (!this.currentProject || this.currentProject.tasks.length === 0) {
            container.innerHTML = '<p class="empty-state">No tasks to display in Gantt chart</p>';
            return;
        }

        // Create Gantt chart SVG
        const svg = this.createGanttSVG(this.currentProject.tasks);
        container.innerHTML = svg;
    }

    createGanttSVG(tasks) {
        const width = 1000;
        const height = tasks.length * 50 + 100;
        const taskHeight = 30;
        const spacing = 50;
        const leftMargin = 250;

        // Find date range
        const dates = tasks.flatMap(t => [t.startDate, t.dueDate].filter(Boolean));
        if (dates.length === 0) return '<p class="empty-state">No dates set for tasks</p>';

        const minDate = new Date(Math.min(...dates.map(d => new Date(d))));
        const maxDate = new Date(Math.max(...dates.map(d => new Date(d))));
        const dateRange = maxDate - minDate || 1;
        const chartWidth = width - leftMargin - 50;

        let svg = `<svg width="${width}" height="${height}" viewBox="0 0 ${width} ${height}" style="font-family: 'Times New Roman', serif; font-weight: bold;">`;

        // Add background
        svg += `<rect width="${width}" height="${height}" fill="#f8f9fa"/>`;

        // Draw header
        svg += `<rect x="0" y="0" width="${width}" height="40" fill="linear-gradient(135deg, #4682B4, #87CEEB)"/>`;
        svg += `<text x="20" y="25" font-size="16" fill="white" font-weight="bold">Task Timeline</text>`;

        // Draw timeline header
        const timelineY = 50;
        svg += `<rect x="${leftMargin}" y="${timelineY}" width="${chartWidth}" height="30" fill="#FFD700" opacity="0.3"/>`;

        // Add date markers
        const dateSteps = Math.max(1, Math.floor(dateRange / (1000 * 60 * 60 * 24 * 7))); // Weekly markers
        for (let i = 0; i <= dateSteps; i++) {
            const date = new Date(minDate.getTime() + (dateRange * i / dateSteps));
            const x = leftMargin + (chartWidth * i / dateSteps);
            svg += `<line x1="${x}" y1="${timelineY}" x2="${x}" y2="${height}" stroke="#87CEEB" stroke-width="1" opacity="0.3"/>`;
            svg += `<text x="${x + 5}" y="${timelineY + 20}" font-size="10" fill="#4682B4">${date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}</text>`;
        }

        // Draw tasks
        tasks.forEach((task, index) => {
            const y = timelineY + 40 + (index * spacing);

            // Task name background
            svg += `<rect x="0" y="${y - 5}" width="${leftMargin - 10}" height="${taskHeight + 10}" fill="white" stroke="#87CEEB" stroke-width="1"/>`;

            // Task name
            svg += `<text x="10" y="${y + 20}" font-size="12" fill="#4682B4" font-weight="bold">${task.name}</text>`;
            svg += `<text x="10" y="${y + 35}" font-size="10" fill="#666">${task.assignee || 'Unassigned'}</text>`;

            if (task.startDate && task.dueDate) {
                const startX = leftMargin + ((new Date(task.startDate) - minDate) / dateRange) * chartWidth;
                const endX = leftMargin + ((new Date(task.dueDate) - minDate) / dateRange) * chartWidth;
                const barWidth = Math.max(endX - startX, 20);

                // Task bar shadow
                svg += `<rect x="${startX + 2}" y="${y + 2}" width="${barWidth}" height="${taskHeight}" fill="black" opacity="0.1" rx="6"/>`;

                // Task bar
                const statusColor = this.getStatusColor(task.status);
                svg += `<rect x="${startX}" y="${y}" width="${barWidth}" height="${taskHeight}" fill="${statusColor}" stroke="#fff" stroke-width="2" rx="6"/>`;

                // Progress indicator
                if (task.status === 'in-progress') {
                    const progressWidth = barWidth * 0.6; // Assume 60% progress for in-progress tasks
                    svg += `<rect x="${startX}" y="${y}" width="${progressWidth}" height="${taskHeight}" fill="${statusColor}" opacity="0.8" rx="6"/>`;
                    svg += `<rect x="${startX + progressWidth}" y="${y}" width="${barWidth - progressWidth}" height="${taskHeight}" fill="${statusColor}" opacity="0.3" rx="6"/>`;
                }

                // Task duration text
                const duration = Math.ceil((new Date(task.dueDate) - new Date(task.startDate)) / (1000 * 60 * 60 * 24));
                svg += `<text x="${startX + barWidth/2}" y="${y + 20}" font-size="10" fill="white" text-anchor="middle" font-weight="bold">${duration}d</text>`;

                // Priority indicator
                const priorityColors = { low: '#87CEEB', medium: '#FFD700', high: '#FFA500', critical: '#FF6B6B' };
                svg += `<circle cx="${startX - 10}" cy="${y + 15}" r="6" fill="${priorityColors[task.priority] || '#87CEEB'}" stroke="white" stroke-width="2"/>`;

            } else {
                svg += `<text x="${leftMargin + 10}" y="${y + 20}" font-size="11" fill="#999" font-style="italic">No dates set</text>`;
            }
        });

        // Add legend
        const legendY = height - 60;
        svg += `<rect x="20" y="${legendY}" width="200" height="50" fill="white" stroke="#87CEEB" stroke-width="1" rx="5"/>`;
        svg += `<text x="30" y="${legendY + 15}" font-size="12" fill="#4682B4" font-weight="bold">Legend:</text>`;

        const statusColors = [
            { status: 'todo', color: '#95a5a6', label: 'To Do' },
            { status: 'in-progress', color: '#4682B4', label: 'In Progress' },
            { status: 'review', color: '#FFD700', label: 'Review' },
            { status: 'done', color: '#FFA500', label: 'Done' }
        ];

        statusColors.forEach((item, i) => {
            const x = 30 + (i * 45);
            svg += `<rect x="${x}" y="${legendY + 25}" width="12" height="12" fill="${item.color}" rx="2"/>`;
            svg += `<text x="${x + 15}" y="${legendY + 35}" font-size="8" fill="#666">${item.label}</text>`;
        });

        svg += '</svg>';
        return svg;
    }

    getStatusColor(status) {
        const colors = {
            'todo': '#95a5a6',
            'in-progress': '#3498db',
            'review': '#f39c12',
            'done': '#27ae60'
        };
        return colors[status] || '#95a5a6';
    }

    loadTaskCalendar() {
        const container = document.getElementById('taskCalendar');
        container.innerHTML = '<p class="empty-state">Task calendar view - Coming soon!</p>';
    }

    loadTaskList() {
        const container = document.getElementById('taskList');
        if (!this.currentProject || this.currentProject.tasks.length === 0) {
            container.innerHTML = '<p class="empty-state">No tasks in this project</p>';
            return;
        }

        container.innerHTML = `
            <div class="task-list-header">
                <div class="task-list-row">
                    <div class="task-col-name">Task</div>
                    <div class="task-col-status">Status</div>
                    <div class="task-col-priority">Priority</div>
                    <div class="task-col-assignee">Assignee</div>
                    <div class="task-col-due">Due Date</div>
                    <div class="task-col-actions">Actions</div>
                </div>
            </div>
            <div class="task-list-body">
                ${this.currentProject.tasks.map(task => `
                    <div class="task-list-row">
                        <div class="task-col-name">
                            <div class="task-name" onclick="projectManager.openTaskDetail('${task.id}')">${task.name}</div>
                            <div class="task-description">${task.description || ''}</div>
                        </div>
                        <div class="task-col-status">
                            <span class="status-badge ${task.status}">${task.status}</span>
                        </div>
                        <div class="task-col-priority">
                            <span class="priority-badge ${task.priority}">${task.priority}</span>
                        </div>
                        <div class="task-col-assignee">${task.assignee || '-'}</div>
                        <div class="task-col-due">${task.dueDate ? this.formatDate(task.dueDate) : '-'}</div>
                        <div class="task-col-actions">
                            <button class="task-action-btn edit-btn" onclick="projectManager.editTask('${task.id}')" title="Edit Task">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="task-action-btn delete-btn" onclick="projectManager.deleteTask('${task.id}')" title="Delete Task">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    }

    loadCalendar() {
        const container = document.getElementById('mainCalendar');
        const currentDate = new Date();
        this.currentCalendarDate = this.currentCalendarDate || currentDate;

        // Update month display
        document.getElementById('currentMonth').textContent =
            this.currentCalendarDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });

        // Setup month navigation
        this.setupCalendarNavigation();

        // Generate calendar HTML
        container.innerHTML = this.generateCalendarHTML();
    }

    setupCalendarNavigation() {
        document.getElementById('prevMonth').onclick = () => {
            this.currentCalendarDate.setMonth(this.currentCalendarDate.getMonth() - 1);
            this.loadCalendar();
        };

        document.getElementById('nextMonth').onclick = () => {
            this.currentCalendarDate.setMonth(this.currentCalendarDate.getMonth() + 1);
            this.loadCalendar();
        };
    }

    generateCalendarHTML() {
        const year = this.currentCalendarDate.getFullYear();
        const month = this.currentCalendarDate.getMonth();

        // Get first day of month and number of days
        const firstDay = new Date(year, month, 1);
        const lastDay = new Date(year, month + 1, 0);
        const daysInMonth = lastDay.getDate();
        const startingDayOfWeek = firstDay.getDay();

        // Get all tasks for this month
        const monthTasks = this.getAllTasksForMonth(year, month);

        let html = `
            <div class="calendar-grid">
                <div class="calendar-header-row">
                    <div class="calendar-day-header">Sun</div>
                    <div class="calendar-day-header">Mon</div>
                    <div class="calendar-day-header">Tue</div>
                    <div class="calendar-day-header">Wed</div>
                    <div class="calendar-day-header">Thu</div>
                    <div class="calendar-day-header">Fri</div>
                    <div class="calendar-day-header">Sat</div>
                </div>
        `;

        // Add empty cells for days before month starts
        for (let i = 0; i < startingDayOfWeek; i++) {
            html += '<div class="calendar-day empty"></div>';
        }

        // Add days of the month
        for (let day = 1; day <= daysInMonth; day++) {
            const dateStr = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
            const dayTasks = monthTasks.filter(task =>
                task.dueDate === dateStr || task.startDate === dateStr
            );

            const isToday = new Date().toDateString() === new Date(year, month, day).toDateString();

            html += `
                <div class="calendar-day ${isToday ? 'today' : ''}" data-date="${dateStr}">
                    <div class="calendar-day-number">${day}</div>
                    <div class="calendar-day-tasks">
                        ${dayTasks.map(task => `
                            <div class="calendar-task" style="background-color: ${task.projectColor}; opacity: 0.8;">
                                <span class="calendar-task-name">${task.name}</span>
                                <span class="calendar-task-project">${task.projectName}</span>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        }

        html += '</div>';
        return html;
    }

    getAllTasksForMonth(year, month) {
        const allTasks = [];
        this.projects.forEach(project => {
            project.tasks.forEach(task => {
                if (task.dueDate || task.startDate) {
                    const taskDate = new Date(task.dueDate || task.startDate);
                    if (taskDate.getFullYear() === year && taskDate.getMonth() === month) {
                        allTasks.push({
                            ...task,
                            projectName: project.name,
                            projectColor: project.color
                        });
                    }
                }
            });
        });
        return allTasks;
    }

    loadAnalytics() {
        this.createProjectProgressChart();
        this.createTaskDistributionChart();
        this.createTimelineChart();
        this.createPerformanceMetricsChart();
    }

    createProjectProgressChart() {
        const ctx = document.getElementById('projectProgressChart').getContext('2d');

        // Destroy existing chart if it exists
        if (this.projectProgressChartInstance) {
            this.projectProgressChartInstance.destroy();
        }

        const projectData = this.projects.map(project => ({
            name: project.name,
            progress: project.progress,
            color: project.color
        }));

        this.projectProgressChartInstance = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: projectData.map(p => p.name),
                datasets: [{
                    label: 'Progress (%)',
                    data: projectData.map(p => p.progress),
                    backgroundColor: projectData.map(p => p.color + '80'),
                    borderColor: projectData.map(p => p.color),
                    borderWidth: 2,
                    borderRadius: 8
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: {
                            font: {
                                family: 'Times New Roman',
                                weight: 'bold'
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        ticks: {
                            font: {
                                family: 'Times New Roman',
                                weight: 'bold'
                            }
                        }
                    },
                    x: {
                        ticks: {
                            font: {
                                family: 'Times New Roman',
                                weight: 'bold'
                            }
                        }
                    }
                }
            }
        });
    }

    createTaskDistributionChart() {
        const ctx = document.getElementById('taskDistributionChart').getContext('2d');

        if (this.taskDistributionChartInstance) {
            this.taskDistributionChartInstance.destroy();
        }

        const statusCounts = {
            'todo': 0,
            'in-progress': 0,
            'review': 0,
            'done': 0
        };

        this.projects.forEach(project => {
            project.tasks.forEach(task => {
                statusCounts[task.status]++;
            });
        });

        this.taskDistributionChartInstance = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['To Do', 'In Progress', 'Review', 'Done'],
                datasets: [{
                    data: [statusCounts.todo, statusCounts['in-progress'], statusCounts.review, statusCounts.done],
                    backgroundColor: ['#87CEEB', '#4682B4', '#FFD700', '#FFA500'],
                    borderColor: ['#4682B4', '#87CEEB', '#FFA500', '#FFD700'],
                    borderWidth: 3
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            font: {
                                family: 'Times New Roman',
                                weight: 'bold'
                            }
                        }
                    }
                }
            }
        });
    }

    createTimelineChart() {
        const ctx = document.getElementById('timelineChart').getContext('2d');

        if (this.timelineChartInstance) {
            this.timelineChartInstance.destroy();
        }

        const monthlyData = this.getMonthlyTaskCompletion();

        this.timelineChartInstance = new Chart(ctx, {
            type: 'line',
            data: {
                labels: monthlyData.labels,
                datasets: [{
                    label: 'Tasks Completed',
                    data: monthlyData.completed,
                    borderColor: '#4682B4',
                    backgroundColor: '#87CEEB40',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4
                }, {
                    label: 'Tasks Created',
                    data: monthlyData.created,
                    borderColor: '#FFD700',
                    backgroundColor: '#FFD70040',
                    borderWidth: 3,
                    fill: false,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: {
                            font: {
                                family: 'Times New Roman',
                                weight: 'bold'
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            font: {
                                family: 'Times New Roman',
                                weight: 'bold'
                            }
                        }
                    },
                    x: {
                        ticks: {
                            font: {
                                family: 'Times New Roman',
                                weight: 'bold'
                            }
                        }
                    }
                }
            }
        });
    }

    createPerformanceMetricsChart() {
        const ctx = document.getElementById('performanceMetrics').getContext('2d');

        if (this.performanceMetricsChartInstance) {
            this.performanceMetricsChartInstance.destroy();
        }

        const metrics = this.calculatePerformanceMetrics();

        this.performanceMetricsChartInstance = new Chart(ctx, {
            type: 'radar',
            data: {
                labels: ['On Time Delivery', 'Task Completion Rate', 'Project Progress', 'Team Productivity', 'Quality Score'],
                datasets: [{
                    label: 'Performance Metrics',
                    data: [metrics.onTime, metrics.completion, metrics.progress, metrics.productivity, metrics.quality],
                    backgroundColor: '#4682B480',
                    borderColor: '#4682B4',
                    borderWidth: 3,
                    pointBackgroundColor: '#FFD700',
                    pointBorderColor: '#FFA500',
                    pointBorderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: {
                            font: {
                                family: 'Times New Roman',
                                weight: 'bold'
                            }
                        }
                    }
                },
                scales: {
                    r: {
                        beginAtZero: true,
                        max: 100,
                        ticks: {
                            font: {
                                family: 'Times New Roman',
                                weight: 'bold'
                            }
                        }
                    }
                }
            }
        });
    }

    getMonthlyTaskCompletion() {
        const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'];
        const completed = [0, 0, 0, 0, 0, 0];
        const created = [0, 0, 0, 0, 0, 0];

        this.projects.forEach(project => {
            project.tasks.forEach(task => {
                const createdDate = new Date(task.createdAt);
                const createdMonth = createdDate.getMonth();
                if (createdMonth < 6) created[createdMonth]++;

                if (task.completedAt) {
                    const completedDate = new Date(task.completedAt);
                    const completedMonth = completedDate.getMonth();
                    if (completedMonth < 6) completed[completedMonth]++;
                }
            });
        });

        return { labels: months, completed, created };
    }

    calculatePerformanceMetrics() {
        const totalTasks = this.projects.reduce((sum, p) => sum + p.tasks.length, 0);
        const completedTasks = this.projects.reduce((sum, p) =>
            sum + p.tasks.filter(t => t.status === 'done').length, 0);
        const onTimeTasks = this.projects.reduce((sum, p) =>
            sum + p.tasks.filter(t =>
                t.status === 'done' && t.dueDate &&
                new Date(t.completedAt) <= new Date(t.dueDate)
            ).length, 0);

        const avgProgress = this.projects.reduce((sum, p) => sum + p.progress, 0) / this.projects.length;

        return {
            onTime: totalTasks > 0 ? Math.round((onTimeTasks / completedTasks) * 100) || 0 : 0,
            completion: totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0,
            progress: Math.round(avgProgress) || 0,
            productivity: Math.min(100, Math.round((completedTasks / 30) * 100)), // Assuming 30 days
            quality: Math.round(85 + Math.random() * 15) // Simulated quality score
        };
    }

    filterProjects(searchTerm) {
        const filteredProjects = this.projects.filter(project =>
            project.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            project.description.toLowerCase().includes(searchTerm.toLowerCase())
        );
        this.displayFilteredProjects(filteredProjects);
    }

    filterProjectsByStatus(status) {
        const filteredProjects = status === 'all' ? 
            this.projects : 
            this.projects.filter(project => project.status === status);
        this.displayFilteredProjects(filteredProjects);
    }

    displayFilteredProjects(projects) {
        const container = document.getElementById('projectsContainer');
        if (projects.length === 0) {
            container.innerHTML = '<p class="empty-state">No projects match your criteria</p>';
            return;
        }
        // Use the same rendering logic as loadProjects but with filtered data
        const originalProjects = this.projects;
        this.projects = projects;
        this.loadProjects();
        this.projects = originalProjects;
    }

    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    formatDate(dateString) {
        if (!dateString) return '';
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', { 
            month: 'short', 
            day: 'numeric', 
            year: 'numeric' 
        });
    }

    saveProjects() {
        localStorage.setItem('projects', JSON.stringify(this.projects));
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'info-circle'}"></i>
            <span>${message}</span>
        `;
        
        // Add to page
        document.body.appendChild(notification);
        
        // Animate in
        setTimeout(() => notification.classList.add('show'), 100);
        
        // Remove after 3 seconds
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }
}

// Initialize the application
const projectManager = new ProjectManager();

// Add some sample data if no projects exist
if (projectManager.projects.length === 0) {
    const sampleProjects = [
        {
            id: 'sample-1',
            name: 'Website Redesign',
            description: 'Complete redesign of company website with modern UI/UX and enhanced user experience',
            startDate: '2024-01-01',
            endDate: '2024-03-31',
            priority: 'high',
            color: '#4682B4',
            status: 'active',
            progress: 65,
            tasks: [
                {
                    id: 'task-1',
                    name: 'Design Mockups',
                    description: 'Create initial design mockups for all pages',
                    priority: 'high',
                    status: 'done',
                    startDate: '2024-01-01',
                    dueDate: '2024-01-15',
                    assignee: 'John Doe',
                    createdAt: '2024-01-01T00:00:00.000Z',
                    completedAt: '2024-01-14T00:00:00.000Z'
                },
                {
                    id: 'task-2',
                    name: 'Frontend Development',
                    description: 'Implement responsive frontend using React',
                    priority: 'high',
                    status: 'in-progress',
                    startDate: '2024-01-16',
                    dueDate: '2024-02-28',
                    assignee: 'Jane Smith',
                    createdAt: '2024-01-16T00:00:00.000Z',
                    completedAt: null
                },
                {
                    id: 'task-3',
                    name: 'Content Migration',
                    description: 'Migrate existing content to new structure',
                    priority: 'medium',
                    status: 'done',
                    startDate: '2024-02-01',
                    dueDate: '2024-02-15',
                    assignee: 'Bob Johnson',
                    createdAt: '2024-01-20T00:00:00.000Z',
                    completedAt: '2024-02-14T00:00:00.000Z'
                },
                {
                    id: 'task-4',
                    name: 'Testing & QA',
                    description: 'Comprehensive testing across all devices',
                    priority: 'high',
                    status: 'review',
                    startDate: '2024-03-01',
                    dueDate: '2024-03-15',
                    assignee: 'Alice Wilson',
                    createdAt: '2024-01-25T00:00:00.000Z',
                    completedAt: null
                }
            ],
            createdAt: '2024-01-01T00:00:00.000Z'
        },
        {
            id: 'sample-2',
            name: 'Mobile App Development',
            description: 'Native iOS and Android app with cross-platform features',
            startDate: '2024-02-01',
            endDate: '2024-06-30',
            priority: 'critical',
            color: '#FFD700',
            status: 'active',
            progress: 35,
            tasks: [
                {
                    id: 'task-5',
                    name: 'App Architecture',
                    description: 'Design scalable app architecture',
                    priority: 'critical',
                    status: 'done',
                    startDate: '2024-02-01',
                    dueDate: '2024-02-10',
                    assignee: 'Mike Chen',
                    createdAt: '2024-02-01T00:00:00.000Z',
                    completedAt: '2024-02-09T00:00:00.000Z'
                },
                {
                    id: 'task-6',
                    name: 'iOS Development',
                    description: 'Develop native iOS application',
                    priority: 'high',
                    status: 'in-progress',
                    startDate: '2024-02-15',
                    dueDate: '2024-04-30',
                    assignee: 'Sarah Lee',
                    createdAt: '2024-02-15T00:00:00.000Z',
                    completedAt: null
                },
                {
                    id: 'task-7',
                    name: 'Android Development',
                    description: 'Develop native Android application',
                    priority: 'high',
                    status: 'todo',
                    startDate: '2024-03-01',
                    dueDate: '2024-05-15',
                    assignee: 'David Kim',
                    createdAt: '2024-02-20T00:00:00.000Z',
                    completedAt: null
                },
                {
                    id: 'task-8',
                    name: 'API Integration',
                    description: 'Integrate with backend APIs',
                    priority: 'medium',
                    status: 'todo',
                    startDate: '2024-04-01',
                    dueDate: '2024-05-30',
                    assignee: 'Emma Davis',
                    createdAt: '2024-02-25T00:00:00.000Z',
                    completedAt: null
                }
            ],
            createdAt: '2024-02-01T00:00:00.000Z'
        },
        {
            id: 'sample-3',
            name: 'Marketing Campaign Q2',
            description: 'Comprehensive digital marketing campaign for Q2 product launch',
            startDate: '2024-03-01',
            endDate: '2024-06-30',
            priority: 'medium',
            color: '#87CEEB',
            status: 'active',
            progress: 20,
            tasks: [
                {
                    id: 'task-9',
                    name: 'Market Research',
                    description: 'Conduct comprehensive market analysis',
                    priority: 'high',
                    status: 'done',
                    startDate: '2024-03-01',
                    dueDate: '2024-03-15',
                    assignee: 'Lisa Wang',
                    createdAt: '2024-03-01T00:00:00.000Z',
                    completedAt: '2024-03-14T00:00:00.000Z'
                },
                {
                    id: 'task-10',
                    name: 'Content Creation',
                    description: 'Create marketing content and assets',
                    priority: 'medium',
                    status: 'in-progress',
                    startDate: '2024-03-15',
                    dueDate: '2024-04-30',
                    assignee: 'Tom Brown',
                    createdAt: '2024-03-15T00:00:00.000Z',
                    completedAt: null
                },
                {
                    id: 'task-11',
                    name: 'Social Media Strategy',
                    description: 'Develop social media marketing strategy',
                    priority: 'medium',
                    status: 'todo',
                    startDate: '2024-04-01',
                    dueDate: '2024-04-15',
                    assignee: 'Amy Johnson',
                    createdAt: '2024-03-20T00:00:00.000Z',
                    completedAt: null
                }
            ],
            createdAt: '2024-03-01T00:00:00.000Z'
        },
        {
            id: 'sample-4',
            name: 'Data Analytics Platform',
            description: 'Build comprehensive analytics dashboard for business intelligence',
            startDate: '2024-01-15',
            endDate: '2024-05-15',
            priority: 'high',
            color: '#FFA500',
            status: 'completed',
            progress: 100,
            tasks: [
                {
                    id: 'task-12',
                    name: 'Database Design',
                    description: 'Design scalable database schema',
                    priority: 'critical',
                    status: 'done',
                    startDate: '2024-01-15',
                    dueDate: '2024-01-30',
                    assignee: 'Alex Rodriguez',
                    createdAt: '2024-01-15T00:00:00.000Z',
                    completedAt: '2024-01-28T00:00:00.000Z'
                },
                {
                    id: 'task-13',
                    name: 'Dashboard Development',
                    description: 'Build interactive analytics dashboard',
                    priority: 'high',
                    status: 'done',
                    startDate: '2024-02-01',
                    dueDate: '2024-04-15',
                    assignee: 'Rachel Green',
                    createdAt: '2024-02-01T00:00:00.000Z',
                    completedAt: '2024-04-12T00:00:00.000Z'
                },
                {
                    id: 'task-14',
                    name: 'Data Visualization',
                    description: 'Implement advanced data visualization',
                    priority: 'medium',
                    status: 'done',
                    startDate: '2024-03-01',
                    dueDate: '2024-05-01',
                    assignee: 'Chris Taylor',
                    createdAt: '2024-03-01T00:00:00.000Z',
                    completedAt: '2024-04-28T00:00:00.000Z'
                }
            ],
            createdAt: '2024-01-15T00:00:00.000Z'
        }
    ];

    projectManager.projects = sampleProjects;
    projectManager.saveProjects();
    projectManager.updateStats();
}
