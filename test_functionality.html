<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ProjectFlow - Functionality Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-item {
            margin: 10px 0;
            padding: 10px;
            border-left: 4px solid #4682B4;
            background: #f8f9fa;
        }
        .status {
            font-weight: bold;
            padding: 2px 8px;
            border-radius: 4px;
            color: white;
        }
        .working { background: #28a745; }
        .broken { background: #dc3545; }
        .untested { background: #6c757d; }
        h1 { color: #4682B4; }
        h2 { color: #2c3e50; }
        .instructions {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>ProjectFlow - Functionality Test Guide</h1>
    
    <div class="instructions">
        <h3>How to Test:</h3>
        <p>1. Open the main application at <a href="http://localhost:8000" target="_blank">http://localhost:8000</a></p>
        <p>2. Go through each test item below and verify the functionality works</p>
        <p>3. Update the status indicators as you test</p>
    </div>

    <div class="test-section">
        <h2>🎯 Navigation & Views</h2>
        <div class="test-item">
            <span class="status untested">UNTESTED</span>
            <strong>Dashboard View:</strong> Click "Dashboard" button or press "1" key
        </div>
        <div class="test-item">
            <span class="status untested">UNTESTED</span>
            <strong>Projects View:</strong> Click "Projects" button or press "2" key
        </div>
        <div class="test-item">
            <span class="status untested">UNTESTED</span>
            <strong>Calendar View:</strong> Click "Calendar" button or press "3" key
        </div>
        <div class="test-item">
            <span class="status untested">UNTESTED</span>
            <strong>Analytics View:</strong> Click "Analytics" button or press "4" key
        </div>
    </div>

    <div class="test-section">
        <h2>⌨️ Keyboard Shortcuts</h2>
        <div class="test-item">
            <span class="status untested">UNTESTED</span>
            <strong>Ctrl/Cmd + N:</strong> Create New Project
        </div>
        <div class="test-item">
            <span class="status untested">UNTESTED</span>
            <strong>Ctrl/Cmd + T:</strong> Create New Task (when in project view)
        </div>
        <div class="test-item">
            <span class="status untested">UNTESTED</span>
            <strong>Escape:</strong> Close Modals
        </div>
        <div class="test-item">
            <span class="status untested">UNTESTED</span>
            <strong>Number Keys 1-4:</strong> Switch between main views
        </div>
    </div>

    <div class="test-section">
        <h2>📋 Project Management</h2>
        <div class="test-item">
            <span class="status untested">UNTESTED</span>
            <strong>Create Project:</strong> Click "New Project" button, fill form, submit
        </div>
        <div class="test-item">
            <span class="status untested">UNTESTED</span>
            <strong>Open Project:</strong> Click on any project card
        </div>
        <div class="test-item">
            <span class="status untested">UNTESTED</span>
            <strong>Project Views:</strong> Switch between Grid and List view
        </div>
        <div class="test-item">
            <span class="status untested">UNTESTED</span>
            <strong>Search Projects:</strong> Use search box to filter projects
        </div>
    </div>

    <div class="test-section">
        <h2>✅ Task Management</h2>
        <div class="test-item">
            <span class="status untested">UNTESTED</span>
            <strong>Create Task:</strong> Click "Add Task" button, fill form, submit
        </div>
        <div class="test-item">
            <span class="status untested">UNTESTED</span>
            <strong>Edit Task:</strong> Click edit button (pencil icon) on task card
        </div>
        <div class="test-item">
            <span class="status untested">UNTESTED</span>
            <strong>Delete Task:</strong> Click delete button (trash icon) on task card
        </div>
        <div class="test-item">
            <span class="status untested">UNTESTED</span>
            <strong>View Task Details:</strong> Click on task title or card
        </div>
        <div class="test-item">
            <span class="status untested">UNTESTED</span>
            <strong>Task Views:</strong> Switch between Kanban, Gantt, Calendar, List, Team views
        </div>
    </div>

    <div class="test-section">
        <h2>🎯 Drag & Drop</h2>
        <div class="test-item">
            <span class="status untested">UNTESTED</span>
            <strong>Kanban Drag & Drop:</strong> Drag tasks between columns (To Do, In Progress, Review, Done)
        </div>
        <div class="test-item">
            <span class="status untested">UNTESTED</span>
            <strong>Visual Feedback:</strong> Task should show dragging state and columns should highlight as drop zones
        </div>
    </div>

    <div class="test-section">
        <h2>👥 Team Management</h2>
        <div class="test-item">
            <span class="status untested">UNTESTED</span>
            <strong>Add Team Member:</strong> Click "Add Member" button, fill form, submit
        </div>
        <div class="test-item">
            <span class="status untested">UNTESTED</span>
            <strong>View Team:</strong> Switch to Team view in project
        </div>
        <div class="test-item">
            <span class="status untested">UNTESTED</span>
            <strong>Assign Tasks:</strong> Select team member when creating/editing tasks
        </div>
    </div>

    <div class="test-section">
        <h2>💬 Comments & Time Tracking</h2>
        <div class="test-item">
            <span class="status untested">UNTESTED</span>
            <strong>Add Comments:</strong> Open task detail, add comment
        </div>
        <div class="test-item">
            <span class="status untested">UNTESTED</span>
            <strong>Time Tracking:</strong> Start/stop timer in task detail modal
        </div>
    </div>

    <div class="test-section">
        <h2>🔔 Notifications & Help</h2>
        <div class="test-item">
            <span class="status untested">UNTESTED</span>
            <strong>Notifications:</strong> Click bell icon to view notifications
        </div>
        <div class="test-item">
            <span class="status untested">UNTESTED</span>
            <strong>Help Modal:</strong> Click help button (question mark icon)
        </div>
        <div class="test-item">
            <span class="status untested">UNTESTED</span>
            <strong>Mark All Read:</strong> Click "Mark all as read" in notifications
        </div>
    </div>

    <div class="test-section">
        <h2>📊 Analytics & Charts</h2>
        <div class="test-item">
            <span class="status untested">UNTESTED</span>
            <strong>Analytics View:</strong> View charts and metrics
        </div>
        <div class="test-item">
            <span class="status untested">UNTESTED</span>
            <strong>Gantt Chart:</strong> View project timeline in Gantt view
        </div>
        <div class="test-item">
            <span class="status untested">UNTESTED</span>
            <strong>Calendar View:</strong> View tasks in calendar format
        </div>
    </div>

    <script>
        // Simple script to toggle status when clicking on test items
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('status')) {
                const status = e.target;
                if (status.classList.contains('untested')) {
                    status.classList.remove('untested');
                    status.classList.add('working');
                    status.textContent = 'WORKING';
                } else if (status.classList.contains('working')) {
                    status.classList.remove('working');
                    status.classList.add('broken');
                    status.textContent = 'BROKEN';
                } else if (status.classList.contains('broken')) {
                    status.classList.remove('broken');
                    status.classList.add('untested');
                    status.textContent = 'UNTESTED';
                }
            }
        });
    </script>
</body>
</html>
